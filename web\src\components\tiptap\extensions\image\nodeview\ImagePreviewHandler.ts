import { getFullImageUrl } from '../ImageResourceManager'

interface EditorInstance {
  isEditable: boolean
  commands: {
    updateAttributes: (type: string, attrs: Record<string, unknown>) => void
  }
}

/**
 * 图片预览处理器
 * 处理图片预览模态框的显示、缩放、关闭等功能
 */
export class ImagePreviewHandler {
  /**
   * 创建图片预览处理器
   * @param img 图片元素
   * @param editor 编辑器实例
   * @param getPos 获取位置函数
   */
  constructor(
    private img: HTMLImageElement,
    private editor: EditorInstance,
    private getPos: () => number,
  ) {}

  /**
   * 处理图片预览
   * @param e 事件对象
   */
  handlePreview = (e: MouseEvent | TouchEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // 创建模态框
    const modal = document.createElement('div')
    modal.classList.add('modal-overlay')

    // 创建预览图
    const previewImg = document.createElement('img')
    previewImg.alt = this.img.alt || '图片预览'
    modal.appendChild(previewImg)
    document.body.appendChild(modal)

    // 触发模态框显示动画
    setTimeout(() => {
      modal.classList.add('modal-overlay-active')
    }, 10)

    const originalSrc = this.img.dataset.originalSrc || ''

    // 检查图片是否已经是原图
    const isAlreadyOriginal =
      !originalSrc.includes('/thumbnail') || this.img.src.indexOf('/thumbnail') === -1

    if (originalSrc.includes('/thumbnail') && !isAlreadyOriginal) {
      this.handleThumbnailPreview(previewImg, modal, originalSrc)
    } else {
      // 已经是原图，直接显示
      previewImg.src = this.img.src
      previewImg.style.opacity = '1'
    }

    // 设置缩放功能
    this.setupZoomControls(previewImg, modal)

    // 设置关闭功能
    this.setupCloseHandlers(modal, previewImg, originalSrc)
  }

  /**
   * 处理缩略图预览
   */
  private handleThumbnailPreview(
    previewImg: HTMLImageElement,
    modal: HTMLDivElement,
    originalSrc: string,
  ) {
    // 处理缩略图预览
    previewImg.src = this.img.src
    previewImg.style.opacity = '0.5'

    // 添加加载动画
    const loadingSpinner = document.createElement('div')
    loadingSpinner.classList.add('loading-spinner')
    modal.appendChild(loadingSpinner)

    // 加载原图
    const originalImg = new Image()
    const animationStartTime = Date.now()
    const minDisplayTime = 500

    originalImg.onload = () => {
      const loadTime = Date.now() - animationStartTime

      const setOriginal = () => {
        loadingSpinner.style.display = 'none'
        previewImg.src = originalImg.src
        previewImg.style.opacity = '1'

        // 将原图URL保存到图片元素，以便关闭模态框后更新
        previewImg.dataset.originalFullUrl = originalImg.src
      }

      if (loadTime < minDisplayTime) {
        setTimeout(setOriginal, minDisplayTime - loadTime)
      } else {
        setOriginal()
      }
    }

    // 获取原图URL
    const originalFullUrl = getFullImageUrl(originalSrc.replace('/thumbnail', ''), false)
    originalImg.src = originalFullUrl
  }

  /**
   * 设置缩放控制
   */
  private setupZoomControls(previewImg: HTMLImageElement, modal: HTMLDivElement) {
    let scale = 1
    const minScale = 0.5
    const maxScale = 3
    const scaleStep = 0.1

    const handleWheel = (e: WheelEvent): void => {
      // 阻止默认滚动行为
      e.preventDefault()

      // 计算缩放方向
      const delta = e.deltaY > 0 ? -scaleStep : scaleStep
      const newScale = Math.max(minScale, Math.min(maxScale, scale + delta))

      if (newScale !== scale) {
        scale = newScale
        previewImg.style.transform = `scale(${scale})`
      }
    }

    // 添加滚轮事件监听器
    modal.addEventListener('wheel', handleWheel, { passive: false })
  }

  /**
   * 设置关闭处理器
   */
  private setupCloseHandlers(
    modal: HTMLDivElement,
    previewImg: HTMLImageElement,
    originalSrc: string,
  ) {
    // 关闭模态框函数
    const closeModal = () => {
      modal.classList.remove('modal-overlay-active')
      modal.addEventListener(
        'transitionend',
        () => {
          if (!modal.classList.contains('modal-overlay-active')) {
            // 如果已经加载了原图，则更新图片元素的src为原图URL
            if (previewImg.dataset.originalFullUrl && originalSrc.includes('/thumbnail')) {
              // 更新图片元素为原图
              this.img.src = previewImg.dataset.originalFullUrl

              // 更新原始路径，移除thumbnail标记
              const newOriginalSrc = originalSrc.replace('/thumbnail', '')
              this.img.dataset.originalSrc = newOriginalSrc

              // 如果需要，可以在这里更新节点属性
              if (typeof this.getPos === 'function' && !this.editor.isEditable) {
                try {
                  this.editor.commands.updateAttributes('image', {
                    src: newOriginalSrc,
                  })
                } catch {}
              }
            }

            // 清理事件监听器
            document.body.removeChild(modal)
            document.removeEventListener('keydown', handleEscKey)
          }
        },
        { once: true },
      )
    }

    // 点击关闭
    modal.addEventListener('click', closeModal, { once: true })

    // ESC关闭
    const handleEscKey = (e: KeyboardEvent): void => {
      if (e.key === 'Escape') {
        closeModal()
      }
    }
    document.addEventListener('keydown', handleEscKey)
  }
}
