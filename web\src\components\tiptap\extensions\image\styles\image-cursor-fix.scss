/**
 * 图片光标和选择问题修复样式
 * 解决图片编辑时的光标定位、控制点偏移和闪烁问题
 */

.ProseMirror {
  // 隐藏 ProseMirror 自动生成的分隔符元素，防止光标闪烁
  .ProseMirror-separator {
    display: none !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    position: absolute !important;
    left: -9999px !important;
  }

  // 隐藏尾随换行元素，防止光标闪烁
  .ProseMirror-trailingBreak {
    display: none !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    position: absolute !important;
    left: -9999px !important;
  }

  // 改善图片与光标的关系
  p {
    // 图片容器样式优化
    .resizable-image-wrapper {
      // 确保图片后有适当的间距，避免光标重叠
      &::after {
        content: '';
        display: inline-block;
        width: 0.1rem;
        height: 1px;
        vertical-align: middle;
      }

      // 选中状态下的样式优化
      &.ProseMirror-selectednode {
        // 确保控制点定位准确
        .resize-handle {
          // 使用更精确的定位
          &.handle-top-left {
            top: -6px;
            left: -6px;
          }

          &.handle-top-right {
            top: -6px;
            right: -6px;
          }

          &.handle-bottom-left {
            bottom: -6px;
            left: -6px;
          }

          &.handle-bottom-right {
            bottom: -6px;
            right: -6px;
          }

          &.handle-top {
            top: -6px;
            left: 50%;
            transform: translateX(-50%);
          }

          &.handle-right {
            right: -6px;
            top: 50%;
            transform: translateY(-50%);
          }

          &.handle-bottom {
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
          }

          &.handle-left {
            left: -6px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }

    // 直接的图片元素样式
    > img {
      // 确保图片后有适当的间距
      margin-right: 0.2rem;
    }
  }

  // 光标样式优化
  caret-color: #2d8cf0;

  // 选择样式优化
  ::selection {
    background-color: rgba(45, 140, 240, 25%);
    color: inherit;
  }

  // 防止在图片行点击时出现不必要的选择
  .resizable-image-wrapper {
    // 确保图片包装器不会干扰光标定位
    position: relative;
    z-index: 1;

    // 图片本身的样式
    img {
      // 确保图片不会影响光标定位
      position: relative;
      z-index: 2;
    }
  }
}

// 编辑模式特定的样式
.ProseMirror[contenteditable="true"] {
  // 在编辑模式下，确保光标可见性
  .resizable-image-wrapper {
    // 为光标留出空间
    &:not(.ProseMirror-selectednode) {
      margin-right: 0.3rem;
    }
  }

  // 改善点击图片行时的光标行为
  p {
    // 确保段落有足够的点击区域
    min-height: 1.5em;
    
    // 当段落包含图片时的特殊处理
    &:has(.resizable-image-wrapper) {
      // 确保有足够的空间放置光标
      padding-right: 0.5rem;
    }
  }
}

// 只读模式特定的样式
.ProseMirror[contenteditable="false"] {
  // 在只读模式下隐藏所有控制点
  .resize-handle {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  // 移除图片的选择样式
  .resizable-image-wrapper.ProseMirror-selectednode {
    outline: none !important;
    box-shadow: none !important;
  }
}

// 移动设备优化
@media (width <= 768px) {
  .ProseMirror {
    // 在移动设备上增加图片与光标的间距
    .resizable-image-wrapper {
      margin-right: 0.4rem;
    }

    // 移动设备上的控制点样式
    .resize-handle {
      width: 16px;
      height: 16px;
      border-width: 3px;

      // 调整移动设备上的控制点偏移
      &.handle-top-left,
      &.handle-top-right,
      &.handle-bottom-left,
      &.handle-bottom-right {
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
      }

      &.handle-top,
      &.handle-bottom {
        top: -8px;
        bottom: -8px;
      }

      &.handle-left,
      &.handle-right {
        left: -8px;
        right: -8px;
      }
    }
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .ProseMirror {
    .resize-handle {
      border-width: 3px;
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 50%);
    }
  }
}

// 减少动画的用户偏好支持
@media (prefers-reduced-motion: reduce) {
  .ProseMirror {
    .resize-handle {
      transition: none !important;
      animation: none !important;
    }

    .resizable-image-wrapper {
      transition: none !important;
    }
  }
}
