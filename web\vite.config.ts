import path from 'path'

import vue from '@vitejs/plugin-vue'
import Jsx from '@vitejs/plugin-vue-jsx'
import { defineConfig } from 'vite'
import Checker from 'vite-plugin-checker'
import viteCompression from 'vite-plugin-compression'
import { nodePolyfills } from 'vite-plugin-node-polyfills'

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'), // 配置 @ 指向 src 目录
    },
  },
  plugins: [
    vue(),
    Jsx(),
    Checker({ typescript: true }),
    // 添加 Node.js polyfills 插件解决 "net" 模块警告
    nodePolyfills({
      // 只包含 net 模块
      include: ['net'],
      // 其他配置保持最小化
      globals: {
        process: true, // net 模块可能需要 process
      },
    }),
    viteCompression({
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz',
    }),
  ],
  define: {
    global: 'globalThis',
  },
  build: {
    // 优化 CSS 分割
    cssCodeSplit: true,
    //禁用源码映射
    sourcemap: false,
    // 启用压缩
    minify: 'terser',
    reportCompressedSize: false,
    // 增加警告阈值
    chunkSizeWarningLimit: 1000,
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    rollupOptions: {
      output: {
        manualChunks(id) {
          // node_modules 中的模块按包名分组
          if (id.includes('node_modules')) {
            return id.toString().split('node_modules/')[1].split('/')[0].toString()
          }
        },
      },
    },
  },
})
