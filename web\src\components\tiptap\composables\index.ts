/**
 * Tiptap 组合式函数统一导出
 * 提供编辑器相关的可复用逻辑
 */

// 核心编辑器功能
export { useEditor, createEditor } from '../core/editor/EditorCore'

// 事件管理
export { useEditorEvents, createEditorEvents } from '../events/EditorEventManager'

// 模态框管理
export { useModal, createEditorModal } from '../modal/EditorModal'

// 全屏功能
export { useFullscreen } from '../extensions/fullscreen'

// 图片相关功能
export { useImageUpload } from '../extensions/image/useImageUpload'
export { useImagePreview } from '../extensions/image/composables/useImagePreview'
export { useImageResize } from '../extensions/image/composables/useImageResize'
export { useImageUploadSuccess } from '../extensions/image/composables/useImageUploadSuccess'
export { useImageEventHandler } from '../extensions/image/composables/useImageEventHandler'

// 图片节点视图相关类
export { ImagePreviewHandler } from '../extensions/image/nodeview/ImagePreviewHandler'
export { ResizeHandleManager } from '../extensions/image/nodeview/ResizeHandleManager'
export { ImageResizeController } from '../extensions/image/nodeview/ImageResizeController'

// 编辑器事件优化器 - 保留实际使用的函数
export {
  createDebouncedEditorHandler,
  createThrottledEditorHandler,
  detectPassiveEventSupport,
  getEditorEventConfig,
} from '../events/EditorEventOptimizer'

// 被动事件处理器 - 保留实际使用的函数
export {
  addPassiveEventListener,
  removePassiveEventListener,
  addImageZoomWheelListener,
  addTouchEventListeners,
} from '../events/PassiveEventHandlers'
