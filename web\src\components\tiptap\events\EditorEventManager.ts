import { reactive } from 'vue'

import type { EditorWithStorage } from '@/components/tiptap/extensions/image/useImageUpload'
import { TIPTAP_SELECTION_UPDATE, TIPTAP_IMAGE_UPDATE } from '@/constants/frequency_key.constants'
import frequencyLimit from '@/utils/frequency-limit'
import logger from '@/utils/log'

import type { Node as ProseMirrorNode } from '@tiptap/pm/model'

/**
 * 编辑器事件状态接口
 */
export interface EditorEventState {
  image: boolean
  bilibili: boolean
}

/**
 * 创建编辑器事件管理器
 * 处理编辑器选择、复制、粘贴、拖放等核心事件
 */
export const createEditorEvents = (
  editor: { value: EditorWithStorage | undefined },
  imageHandleCallback: (
    editor: EditorWithStorage,
    files: File[],
    attrs: { src?: string; [key: string]: unknown },
    fileBucket: string,
    useThumbnail: boolean,
  ) => void,
  fileBucket: string,
  useThumbnail: boolean,
) => {
  // 当前选择的菜单项类型
  const selectBubbleMenu = reactive<EditorEventState>({
    image: false,
    bilibili: false,
  })

  /**
   * 设置编辑器事件监听
   */
  const setupEditorEvents = () => {
    if (!editor.value) return

    // 编辑器更新事件 - 优化性能，减少 DOM 操作
    editor.value.on('update', () => {
      // 非编辑模式下移除所有选中状态和边框
      if (!editor.value?.isEditable) {
        // 使用 nextTick 替代 requestAnimationFrame，更符合 Vue 的响应式系统
        Promise.resolve().then(() => {
          try {
            // 移除所有选中状态的节点，不仅仅是图片
            const selectedNodes = document.querySelectorAll('.ProseMirror-selectednode')
            selectedNodes.forEach((node) => {
              node.classList.remove('ProseMirror-selectednode')
            })
          } catch (error) {
            logger.error('Error removing selection in readonly mode:', error)
          }
        })
      }
    })

    // 在只读模式下拦截可能导致节点选择的事件
    if (!editor.value.isEditable) {
      const editorElement = editor.value.view.dom

      // 拦截 mousedown 事件，防止节点选择
      const handleMouseDown = (e: MouseEvent) => {
        if (e.ctrlKey || e.metaKey) {
          // 阻止 Ctrl+点击导致的节点选择
          e.preventDefault()
          e.stopPropagation()
        }
      }

      editorElement.addEventListener('mousedown', handleMouseDown, true)

      // 清理函数
      const cleanup = () => {
        editorElement.removeEventListener('mousedown', handleMouseDown, true)
      }

      // 存储清理函数，以便后续调用
      if (!editorElement.dataset.cleanupRegistered) {
        editorElement.dataset.cleanupRegistered = 'true'
        // 当编辑器销毁时清理事件监听器
        editor.value.on('destroy', cleanup)
      }
    }

    // 选择更新事件 - 使用统一的防抖工具
    editor.value.on('selectionUpdate', () => {
      const selection = editor.value?.state.selection
      const node = (selection as { node?: ProseMirrorNode })?.node

      // 重置所有菜单标志
      selectBubbleMenu.image = false
      selectBubbleMenu.bilibili = false

      if (node?.type) {
        // 始终保持图片的bubble menu关闭
        selectBubbleMenu.image = false
        selectBubbleMenu.bilibili = node.type.name === 'bilibili'

        // 优化图片选择滚动逻辑
        if (node.type.name === 'image') {
          // 使用统一的防抖工具
          frequencyLimit.debounce(
            TIPTAP_SELECTION_UPDATE,
            () => {
              const selectedNode = document.querySelector('.ProseMirror-selectednode')
              if (selectedNode) {
                // 检查元素是否在视口内，避免不必要的滚动
                const rect = selectedNode.getBoundingClientRect()
                const isInViewport = rect.top >= 0 && rect.bottom <= window.innerHeight

                if (!isInViewport) {
                  selectedNode.scrollIntoView({
                    behavior: 'auto',
                    block: 'nearest',
                  })
                }
              }
            },
            150,
          )
        }
      }
    })

    // 粘贴事件处理
    editor.value.on('paste', (props) => {
      if (!editor.value) return
      const attrs = props.slice.content.firstChild?.attrs || {}
      const files = Array.from(props.event.clipboardData?.files || []) as File[]
      const hasImageFiles = files.some((file) => file.type.startsWith('image/'))

      // 只在有图片文件时进行处理
      if (hasImageFiles) {
        logger.debug('Detected image files in clipboard')
        imageHandleCallback(editor.value, files, attrs, fileBucket, useThumbnail)
      }
    })

    // 拖放事件处理
    editor.value.on('drop', (props) => {
      if (!editor.value) return
      const attrs = props.slice.content.firstChild?.attrs || {}
      const files = Array.from(props.event.dataTransfer?.files || []) as File[]
      const hasImageFiles = files.some((file) => file.type.startsWith('image/'))

      // 只在有图片文件时进行处理
      if (hasImageFiles) {
        logger.debug('Detected image files in drop event')
        imageHandleCallback(editor.value, files, attrs, fileBucket, useThumbnail)
      }
    })

    // 事务处理（编辑器内容变化）- 使用统一的防抖工具
    let lastSelectionState: string | null = null

    editor.value.on('transaction', (params: unknown) => {
      const transactionParams = params as { transaction?: { selectionSet?: boolean } }

      // 只在编辑模式且选择发生变化时处理
      if (!editor.value?.isEditable || !transactionParams.transaction?.selectionSet) {
        return
      }

      // 获取当前选择状态的简化表示
      const currentSelection =
        editor.value.state.selection.from + '-' + editor.value.state.selection.to

      // 如果选择状态没有变化，跳过处理
      if (currentSelection === lastSelectionState) {
        return
      }

      lastSelectionState = currentSelection

      // 使用统一的防抖工具
      frequencyLimit.debounce(
        TIPTAP_IMAGE_UPDATE,
        () => {
          try {
            // 使用事件委托方式，减少 DOM 查询
            const editorElement = editor.value?.view?.dom
            if (!editorElement) return

            // 批量处理所有图片包装器
            const allImageWrappers = editorElement.querySelectorAll('.resizable-image-wrapper')

            allImageWrappers.forEach((wrapper: Element) => {
              const isSelected = wrapper.classList.contains('ProseMirror-selectednode')
              const handles = wrapper.querySelectorAll('.resize-handle')

              // 批量更新控制点状态
              handles.forEach((handle: Element) => {
                const htmlHandle = handle as HTMLElement
                const shouldShow = isSelected

                // 只在状态需要改变时才更新 DOM
                const isCurrentlyVisible = htmlHandle.style.display !== 'none'
                if (shouldShow !== isCurrentlyVisible) {
                  if (shouldShow) {
                    htmlHandle.style.display = 'block'
                    htmlHandle.style.visibility = 'visible'
                    htmlHandle.style.opacity = '1'
                    htmlHandle.style.pointerEvents = 'all'
                  } else {
                    htmlHandle.style.display = 'none'
                    htmlHandle.style.visibility = 'hidden'
                    htmlHandle.style.opacity = '0'
                    htmlHandle.style.pointerEvents = 'none'
                  }
                }
              })
            })
          } catch (error) {
            logger.error('Error updating image handles:', error)
          }
        },
        100,
      )
    })
  }

  return {
    selectBubbleMenu,
    setupEditorEvents,
  }
}

/**
 * 使用编辑器事件管理器
 */
export const useEditorEvents = (
  editor: { value: EditorWithStorage | undefined },
  imageHandleCallback: (
    editor: EditorWithStorage,
    files: File[],
    attrs: { src?: string; [key: string]: unknown },
    fileBucket: string,
    useThumbnail: boolean,
  ) => void,
  fileBucket: string,
  useThumbnail: boolean,
) => {
  const { selectBubbleMenu, setupEditorEvents } = createEditorEvents(
    editor,
    imageHandleCallback,
    fileBucket,
    useThumbnail,
  )

  return {
    selectBubbleMenu,
    setupEditorEvents,
  }
}
