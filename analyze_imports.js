// 分析项目中的导入关系，找出未被使用的文件
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 项目根目录
const rootDir = path.resolve('.');
const webDir = path.join(rootDir, 'web');
const srcDir = path.join(webDir, 'src');

// 存储所有源文件
const allFiles = new Set();
// 存储所有被导入的文件
const importedFiles = new Set();
// 存储特殊文件（入口文件、配置文件等）
const specialFiles = new Set();
// 存储类型定义文件
const typeFiles = new Set();
// 存储常量文件
const constantFiles = new Set();

// 添加已知的特殊文件
specialFiles.add(path.join(webDir, 'src', 'main.ts').toLowerCase());
specialFiles.add(path.join(webDir, 'src', 'App.vue').toLowerCase());
specialFiles.add(path.join(webDir, 'src', 'router', 'index.ts').toLowerCase());
specialFiles.add(path.join(webDir, 'src', 'stores', 'index.ts').toLowerCase());
specialFiles.add(path.join(webDir, 'src', 'config', 'index.ts').toLowerCase());

// 递归获取所有源文件
function getAllFiles(dir, fileExtensions = ['.ts', '.js', '.vue', '.tsx', '.jsx']) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      getAllFiles(filePath, fileExtensions);
    } else if (fileExtensions.includes(path.extname(file).toLowerCase())) {
      const lowerPath = filePath.toLowerCase();
      allFiles.add(lowerPath);

      // 识别类型定义文件
      if (lowerPath.includes('.types.ts') || lowerPath.includes('\\types\\')) {
        typeFiles.add(lowerPath);
      }

      // 识别常量文件
      if (lowerPath.includes('.constants.ts') || lowerPath.includes('\\constants\\')) {
        constantFiles.add(lowerPath);
      }
    }
  });
}

// 分析文件中的导入语句
function analyzeImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // 匹配 import 语句
    const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+[^,]+|[^,{}\s]+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+[^,]+|[^,{}\s]+))*\s+from\s+)?['"]([^'"]+)['"]/g;

    // 匹配动态导入
    const dynamicImportRegex = /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g;

    // 匹配 require 语句
    const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;

    let match;

    // 处理普通导入
    while ((match = importRegex.exec(content)) !== null) {
      resolveImportPath(filePath, match[1]);
    }

    // 处理动态导入
    while ((match = dynamicImportRegex.exec(content)) !== null) {
      resolveImportPath(filePath, match[1]);
    }

    // 处理 require 导入
    while ((match = requireRegex.exec(content)) !== null) {
      resolveImportPath(filePath, match[1]);
    }

    // 检查 Vue 组件中的导入
    if (filePath.endsWith('.vue')) {
      // 匹配 Vue 组件中的 components 属性
      const componentsRegex = /components\s*:\s*{([^}]*)}/g;
      while ((match = componentsRegex.exec(content)) !== null) {
        const componentsBlock = match[1];
        const componentNameRegex = /['"]?(\w+)['"]?\s*:/g;
        let componentMatch;
        while ((componentMatch = componentNameRegex.exec(componentsBlock)) !== null) {
          // 这里只能获取组件名，无法直接获取导入路径
          // 需要进一步分析文件内容来确定组件的导入路径
        }
      }
    }
  } catch (error) {
    console.error(`Error analyzing imports in ${filePath}:`, error.message);
  }
}

// 解析导入路径
function resolveImportPath(currentFilePath, importPath) {
  // 忽略外部依赖
  if (importPath.startsWith('@/') || importPath.startsWith('./') || importPath.startsWith('../')) {
    let resolvedPath;

    if (importPath.startsWith('@/')) {
      // 处理 @ 别名
      resolvedPath = path.join(srcDir, importPath.substring(2));
    } else {
      // 处理相对路径
      resolvedPath = path.join(path.dirname(currentFilePath), importPath);
    }

    // 处理没有扩展名的导入
    if (!path.extname(resolvedPath)) {
      // 尝试常见的扩展名
      const extensions = ['.ts', '.js', '.vue', '.tsx', '.jsx'];
      for (const ext of extensions) {
        const pathWithExt = resolvedPath + ext;
        if (allFiles.has(pathWithExt.toLowerCase())) {
          importedFiles.add(pathWithExt.toLowerCase());
          return;
        }
      }

      // 检查是否是目录导入（index 文件）
      for (const ext of extensions) {
        const indexPath = path.join(resolvedPath, `index${ext}`);
        if (allFiles.has(indexPath.toLowerCase())) {
          importedFiles.add(indexPath.toLowerCase());
          return;
        }
      }
    } else if (allFiles.has(resolvedPath.toLowerCase())) {
      importedFiles.add(resolvedPath.toLowerCase());
    }
  }
}

// 检查文件内容中是否有字符串引用
function checkStringReferences(filePath, targetFile) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(targetFile, path.extname(targetFile));

    // 检查文件名是否在内容中被引用（作为字符串）
    return content.includes(`'${fileName}'`) || content.includes(`"${fileName}"`);
  } catch (error) {
    return false;
  }
}

// 主函数
function findUnusedFiles() {
  console.log('正在分析项目中的导入关系...');

  // 获取所有源文件
  getAllFiles(srcDir);
  console.log(`找到 ${allFiles.size} 个源文件`);
  console.log(`其中类型定义文件: ${typeFiles.size} 个`);
  console.log(`常量文件: ${constantFiles.size} 个`);

  // 分析每个文件的导入
  allFiles.forEach(file => {
    analyzeImports(file);
  });

  // 找出未被导入的文件
  const unusedFiles = new Set();
  const potentiallyUsedFiles = new Set();

  allFiles.forEach(file => {
    if (!importedFiles.has(file) && !specialFiles.has(file)) {
      // 类型文件和常量文件可能通过非常规方式使用
      if (typeFiles.has(file) || constantFiles.has(file)) {
        potentiallyUsedFiles.add(file);
      } else {
        // 检查是否有通过字符串引用
        let isReferencedAsString = false;
        for (const sourceFile of allFiles) {
          if (sourceFile !== file && checkStringReferences(sourceFile, file)) {
            isReferencedAsString = true;
            break;
          }
        }

        if (isReferencedAsString) {
          potentiallyUsedFiles.add(file);
        } else {
          unusedFiles.add(file);
        }
      }
    }
  });

  console.log(`\n可能未被使用的文件 (${unusedFiles.size}):`);
  unusedFiles.forEach(file => {
    console.log(file.replace(rootDir + path.sep, ''));
  });

  console.log(`\n需要进一步检查的文件 (${potentiallyUsedFiles.size}):`);
  potentiallyUsedFiles.forEach(file => {
    console.log(file.replace(rootDir + path.sep, ''));
  });

  return {
    allFiles: Array.from(allFiles),
    importedFiles: Array.from(importedFiles),
    unusedFiles: Array.from(unusedFiles),
    potentiallyUsedFiles: Array.from(potentiallyUsedFiles)
  };
}

// 执行分析
findUnusedFiles();
