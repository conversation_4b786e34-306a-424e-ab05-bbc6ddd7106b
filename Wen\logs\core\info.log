[1;35m19:22:22.989[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:22:23.031[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.c.<PERSON>[0;39m - [36m[logStarting,53][0;39m - Starting WenCoreApp using Java 17.0.15 with PID 18180 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-core\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:22:23.032[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.c.<PERSON>pp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:22:23.653[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:22:23.654[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:22:23.838[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:22:23.841[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:22:23.876[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
[1;35m19:22:24.048[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=31cdb5cd-2f02-36c4-8a52-5d12b79ea847
[1;35m19:22:24.710[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 20003 (http)
[1;35m19:22:24.722[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-20003"]
[1;35m19:22:24.723[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m19:22:24.723[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.42]
[1;35m19:22:24.769[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m19:22:24.769[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 1704 ms
[1;35m19:22:24.940[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.s.b.a.DruidDataSourceAutoConfigure[0;39m - [36m[dataSource,68][0;39m - Init DruidDataSource
[1;35m19:22:24.991[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,824][0;39m - {dataSource-1} inited
[1;35m19:22:25.789[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m19:22:26.121[0;39m [32m[redisson-netty-1-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for 192.168.1.5/192.168.1.5:6379
[1;35m19:22:26.420[0;39m [32m[redisson-netty-1-19][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 24 connections initialized for 192.168.1.5/192.168.1.5:6379
[1;35m19:22:26.758[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.SentinelWebMvcConfigurer[0;39m - [36m[addInterceptors,52][0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[1;35m19:22:27.120[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.m.e.s.MybatisPlusApplicationContextAware[0;39m - [36m[setApplicationContext,40][0;39m - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6587305a
[1;35m19:22:27.613[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 19 endpoints beneath base path '/actuator'
[1;35m19:22:27.681[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.m.s.b.SimpleBrokerMessageHandler[0;39m - [36m[info,117][0;39m - Starting...
[1;35m19:22:27.681[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.m.s.b.SimpleBrokerMessageHandler[0;39m - [36m[info,117][0;39m - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6b00587b]]
[1;35m19:22:27.682[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.m.s.b.SimpleBrokerMessageHandler[0;39m - [36m[info,117][0;39m - Started.
[1;35m19:22:27.682[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Starting ProtocolHandler ["http-nio-20003"]
[1;35m19:22:27.690[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[start,243][0;39m - Tomcat started on port 20003 (http) with context path '/'
[1;35m19:22:28.047[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.c.WenCoreApp[0;39m - [36m[logStarted,59][0;39m - Started WenCoreApp in 6.752 seconds (process running for 8.568)
[1;35m19:22:28.591[0;39m [32m[RMI TCP Connection(4)-*************][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[1;35m19:22:28.591[0;39m [32m[RMI TCP Connection(4)-*************][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,532][0;39m - Initializing Servlet 'dispatcherServlet'
[1;35m19:22:28.593[0;39m [32m[RMI TCP Connection(4)-*************][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,554][0;39m - Completed initialization in 2 ms
[1;35m19:22:28.617[0;39m [32m[RMI TCP Connection(5)-*************][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m19:23:27.664[0;39m [32m[MessageBroker-1][0;39m [34mINFO [0;39m [36mo.s.w.s.c.WebSocketMessageBrokerStats[0;39m - [36m[lambda$initLoggingTask$0,156][0;39m - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
