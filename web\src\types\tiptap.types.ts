import type { <PERSON><PERSON>, <PERSON> } from '@tiptap/pm/model'
import type { Editor } from '@tiptap/vue-3'

export interface EditorWithFormatPainter extends Editor {
  storage: Editor['storage'] & {
    formatPainter: {
      isActive: boolean
      sourceNode: Node | null
      sourceMarks: Mark[] | null
    }
  }
}

export interface EditorWithStorage extends Editor {
  storage: Editor['storage'] & {
    image?: {
      transformSrc?: (src: string) => string
      getFullUrl?: (src: string) => string
    }
    markdown?: {
      getMarkdown: () => string
    }
    formatPainter?: {
      isActive: boolean
      sourceNode: Node | null
      sourceMarks: Mark[] | null
    }
  }
}
