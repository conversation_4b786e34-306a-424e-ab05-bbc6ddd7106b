import { Extension } from '@tiptap/core'
import { <PERSON>lug<PERSON>, Plugin<PERSON>ey, Transaction } from '@tiptap/pm/state'
import type { Editor, RawCommands } from '@tiptap/core'
import type { Step } from '@tiptap/pm/transform'
import type { Slice } from '@tiptap/pm/model'

export interface CharacterCountOptions {
  limit?: number
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    characterCount: {
      getCharacterCount: () => ReturnType
    }
  }
}

interface CharacterCountStorage {
  characters: number
}

interface ReplaceStep extends Step {
  from: number
  to: number
  slice: Slice
}

export const CharacterCountExtension = Extension.create<CharacterCountOptions>({
  name: 'characterCount',

  addOptions() {
    return {
      limit: undefined,
    }
  },

  addStorage() {
    return {
      characters: 0,
    } as CharacterCountStorage
  },

  addCommands() {
    return {
      getCharacterCount:
        () =>
        ({ editor }: { editor: Editor }): boolean => {
          // 获取字符数并存储在编辑器的状态中
          const count = (editor.storage.characterCount as CharacterCountStorage).characters
          // 返回 true 表示命令执行成功
          return true
        },
    } as Partial<RawCommands>
  },

  addProseMirrorPlugins() {
    const extension = this

    // 计算内容的字符数
    const calculateCharacters = (doc: any) => {
      let characterCount = 0
      doc.descendants((node: any) => {
        if (node.isText) {
          characterCount += node.text?.length || 0
        } else if (node.type.name === 'image') {
          characterCount += 100
        } else if (node.type.name === 'bilibili') {
          characterCount += 800
        }
      })
      return characterCount
    }

    return [
      new Plugin({
        key: new PluginKey('characterCount'),
        view: () => ({
          update: (view) => {
            const { doc } = view.state
            const characterCount = calculateCharacters(doc)
            const storage = extension.storage as CharacterCountStorage
            storage.characters = characterCount
          },
        }),
        props: {
          // 处理输入事件
          handleKeyDown: (view, event) => {
            // 如果没有设置字符限制，允许所有操作
            if (!extension.options.limit) {
              return false
            }

            const currentCount = (extension.storage as CharacterCountStorage).characters
            // 如果当前字符数未达到限制，允许所有操作
            if (currentCount < extension.options.limit) {
              return false
            }

            // 定义允许的按键和组合键
            const isAllowedKey = (event: KeyboardEvent): boolean => {
              // 基础编辑按键
              const basicKeys = [
                'Backspace',  // 退格
                'Delete',     // 删除
                'ArrowLeft',  // 左方向键
                'ArrowRight', // 右方向键
                'ArrowUp',    // 上方向键
                'ArrowDown',  // 下方向键
                'Home',       // 行首
                'End',        // 行尾
                'Tab',        // 制表符
                'Escape',     // 退出
                'Enter',      // 换行
              ]

              // 组合键
              const isCombinationKey = 
                // 全选
                (event.ctrlKey && event.key.toLowerCase() === 'a') ||
                // 复制
                (event.ctrlKey && event.key.toLowerCase() === 'c') ||
                // 剪切
                (event.ctrlKey && event.key.toLowerCase() === 'x') ||
                // 撤销
                (event.ctrlKey && event.key.toLowerCase() === 'z') ||
                // 重做
                (event.ctrlKey && event.key.toLowerCase() === 'y') ||
                // 文本选择（Shift + 方向键/Home/End）
                (event.shiftKey && ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key))

              // 如果是基础按键或组合键，则允许操作
              return basicKeys.includes(event.key) || isCombinationKey
            }

            // 如果是不允许的按键，阻止输入
            if (!isAllowedKey(event)) {
              event.preventDefault()
              return true
            }

            // 允许的按键继续执行
            return false
          },
          // 处理粘贴事件
          handlePaste: (view, event) => {
            if (!extension.options.limit) {
              return false
            }

            const currentCount = (extension.storage as CharacterCountStorage).characters
            if (currentCount >= extension.options.limit) {
              event.preventDefault()
              return true
            }

            const clipboardData = event.clipboardData
            if (!clipboardData) {
              return false
            }

            const text = clipboardData.getData('text/plain')
            if (!text) {
              return false
            }

            const newCount = currentCount + text.length
            if (newCount > extension.options.limit) {
              event.preventDefault()
              return true
            }

            return false
          },
          // 处理输入法事件
          handleTextInput: (view, from, to, text) => {
            if (!extension.options.limit) {
              return false
            }

            const currentCount = (extension.storage as CharacterCountStorage).characters
            if (currentCount >= extension.options.limit) {
              return true
            }

            const newCount = currentCount + text.length - (to - from)
            if (newCount > extension.options.limit) {
              return true
            }

            return false
          },
        },
        appendTransaction: (transactions, oldState, newState) => {
          if (!extension.options.limit) {
            return null
          }

          const currentCount = (extension.storage as CharacterCountStorage).characters
          if (currentCount >= extension.options.limit) {
            const tr = newState.tr as Transaction
            let willIncrease = false

            tr.steps.forEach((step: Step) => {
              if (step instanceof Object && 'from' in step && 'to' in step && 'slice' in step) {
                const replaceStep = step as ReplaceStep
                const oldSize = replaceStep.to - replaceStep.from
                const newSize = replaceStep.slice.content.size
                if (newSize > oldSize) {
                  willIncrease = true
                }
              }
            })

            if (willIncrease) {
              return null
            }
          }

          return null
        },
      }),
    ]
  },
}) 