import logger from '@/utils/log'
class Json {
  // 解析 JSON 字符串为对象，增强容错处理
  static parse<T>(jsonString: string): T | null {
    // 处理非字符串输入
    if (typeof jsonString !== 'string') {
      logger.warn('Non-string input to JSON.parse:', typeof jsonString)
      return null
    }

    // 处理空字符串
    if (!jsonString || jsonString.trim() === '') {
      logger.warn('Empty string passed to JSON.parse')
      return null
    }

    // 特殊处理带有@符号的字符串，可能是非标准JSON格式的社交内容或提及功能
    if (jsonString.includes('@')) {
      logger.debug('Detected @ symbol in string, trying to sanitize')
      try {
        // 处理可能的mention格式，例如"@afei : {"key": "value"}"
        const mentionPattern = /@([^:]+)\s*:\s*(\{.*\})/
        const match = jsonString.match(mentionPattern)

        if (match && match[2]) {
          // 尝试解析提及后面的JSON部分
          const jsonPart = match[2]
          return JSON.parse(jsonPart) as T
        }

        // 如果上面的匹配失败，尝试找到可能的JSON部分
        const jsonStartIndex = jsonString.indexOf('{')
        const jsonEndIndex = jsonString.lastIndexOf('}')

        if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
          const possibleJson = jsonString.substring(jsonStartIndex, jsonEndIndex + 1)
          return JSON.parse(possibleJson) as T
        }
      } catch (error) {
        logger.debug('Failed to extract JSON from string with @ symbol:', error)
      }
    }

    try {
      // 尝试解析JSON
      return JSON.parse(jsonString) as T
    } catch (error) {
      // 记录详细错误信息便于调试
      logger.warn('Invalid JSON format:', error instanceof Error ? error.message : 'Unknown error')
      logger.debug(
        'Problem JSON string:',
        jsonString.length > 100 ? jsonString.substring(0, 97) + '...' : jsonString,
      )
      return null
    }
  }

  // 将对象转换为 JSON 字符串
  static stringify(obj: unknown, pretty: boolean = false): string {
    try {
      return pretty ? JSON.stringify(obj, null, 2) : JSON.stringify(obj)
    } catch (error) {
      logger.error('JSON stringify error:', error)
      return ''
    }
  }

  // 深拷贝对象或数组
  static deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj))
  }

  // 判断是否是有效的 JSON 字符串
  static isValidJson(jsonString: string): boolean {
    try {
      JSON.parse(jsonString)
      return true
    } catch {
      return false
    }
  }

  // 获取 JSON 对象的某个属性，支持路径式访问（例如 "a.b.c"）
  static getProperty(obj: any, path: string): any {
    return path.split('.').reduce((acc, part) => (acc ? acc[part] : undefined), obj)
  }

  // 设置 JSON 对象的某个属性，支持路径式访问（例如 "a.b.c"）
  static setProperty(obj: any, path: string, value: any): void {
    const keys = path.split('.')
    let current = obj
    keys.forEach((key, index) => {
      if (index === keys.length - 1) {
        current[key] = value
      } else {
        if (!current[key]) {
          current[key] = {}
        }
        current = current[key]
      }
    })
  }
}
export default Json
