import { darkTheme } from 'naive-ui'
import { ref } from 'vue'

import localStorage from '@/utils/local-storage'

// 主题存储键名
export const THEME_KEY = 'wen-theme'

// 主题类型
export enum ThemeType {
  LIGHT = 'light',
  DARK = 'dark',
}

// 主题名称映射
export const THEME_NAMES = {
  [ThemeType.LIGHT]: '浅色模式',
  [ThemeType.DARK]: '暗色模式',
}

// 主题对象映射
export const THEME_OBJECTS = {
  [ThemeType.LIGHT]: null, // null 代表默认的浅色主题
  [ThemeType.DARK]: darkTheme,
}

// 当前活动主题
export const activeTheme = ref<ThemeType>(ThemeType.LIGHT)

// 主题切换中状态标记
export const isThemeChanging = ref<boolean>(false)

// 亮色主题的CSS变量值
const lightThemeVars = {
  '--white': '#fff',
  '--white-1': '#f0f0f0',
  '--white-2': '#ddd',
  '--creamy-white-1': '#eeece4',
  '--creamy-white-2': '#e4e1d8',
  '--creamy-white-3': '#dcd8ca',
  '--black': '#2e2b29',
  '--black-contrast': '#110f0e',
  '--gray-1': 'rgba(61, 37, 20, 0.05)',
  '--gray-2': 'rgba(61, 37, 20, 0.08)',
  '--gray-3': 'rgba(61, 37, 20, 0.12)',
  '--gray-4': 'rgba(53, 38, 28, 0.3)',
  '--gray-5': 'rgba(28, 25, 23, 0.6)',
  '--blue': '#4ba3fd',
  '--blue-light': '#e6f3ff',
  '--shadow': '0 0.25rem 0.6rem rgba(0, 0, 0, 0.1)',
  // 代码高亮颜色 - 浅色主题
  '--code-comment': '#6a737d',
  '--code-keyword': '#d73a49',
  '--code-string': '#032f62',
  '--code-number': '#005cc5',
  '--code-function': '#6f42c1',
  '--code-variable': '#005cc5',
  '--code-tag': '#22863a',
  '--code-attribute': '#22863a',
  '--code-builtin': '#6f42c1',
  '--code-meta': '#6a737d',
  '--code-deletion-bg': '#ffeef0',
  '--code-deletion-color': '#b31d28',
  '--code-addition-bg': '#f0fff4',
  '--code-addition-color': '#22863a',
  '--code-text': '#24292e',
}

// 暗色主题的CSS变量值
const darkThemeVars = {
  '--white': '#121212',
  '--white-1': '#242424',
  '--white-2': '#363636',
  '--creamy-white-1': '#1a1a1a',
  '--creamy-white-2': '#262626',
  '--creamy-white-3': '#333333',
  '--black': '#e0e0e0',
  '--black-contrast': '#ffffff',
  '--gray-1': 'rgba(200, 200, 200, 0.05)',
  '--gray-2': 'rgba(200, 200, 200, 0.08)',
  '--gray-3': 'rgba(200, 200, 200, 0.12)',
  '--gray-4': 'rgba(200, 200, 200, 0.3)',
  '--gray-5': 'rgba(200, 200, 200, 0.6)',
  '--blue': '#4ba3fd',
  '--blue-light': '#2a3745',
  '--shadow': '0 0.25rem 0.6rem rgba(255, 255, 255, 0.1)',
  // 代码高亮颜色 - 暗色主题
  '--code-comment': '#8b949e',
  '--code-keyword': '#ff7b72',
  '--code-string': '#a5d6ff',
  '--code-number': '#79c0ff',
  '--code-function': '#d2a8ff',
  '--code-variable': '#79c0ff',
  '--code-tag': '#7ee787',
  '--code-attribute': '#7ee787',
  '--code-builtin': '#d2a8ff',
  '--code-meta': '#8b949e',
  '--code-deletion-bg': '#490202',
  '--code-deletion-color': '#ffdcd7',
  '--code-addition-bg': '#033a16',
  '--code-addition-color': '#aff5b4',
  '--code-text': '#e6edf3',
}

// 加载保存的主题 - CSS变量版本
export const loadSavedTheme = () => {
  const savedTheme = localStorage.get(THEME_KEY)
  if (savedTheme && Object.values(ThemeType).includes(savedTheme as ThemeType)) {
    activeTheme.value = savedTheme as ThemeType
    applyThemeVars(savedTheme as ThemeType)
  }
}

// 切换主题 - 高性能混合版本
export const toggleTheme = () => {
  const newTheme = activeTheme.value === ThemeType.LIGHT ? ThemeType.DARK : ThemeType.LIGHT

  // 设置主题切换状态
  isThemeChanging.value = true

  // 设置状态
  activeTheme.value = newTheme
  localStorage.set(THEME_KEY, newTheme)

  // 立即应用主题 - 优先更新重要组件然后再更新其他部分
  applyThemeVars(newTheme)

  // 优先更新特定组件
  prioritizeComponents(newTheme)

  // 短暂延迟后重置状态
  setTimeout(() => {
    isThemeChanging.value = false
  }, 50) // 减少延迟时间

  return newTheme
}

// 应用主题到文档 - 混合版本
const applyThemeVars = (theme: ThemeType) => {
  const root = document.documentElement
  const vars = theme === ThemeType.LIGHT ? lightThemeVars : darkThemeVars
  const isDark = theme === ThemeType.DARK

  // 先设置CSS变量，这是高性能的部分
  for (const [key, value] of Object.entries(vars)) {
    root.style.setProperty(key, value)
  }

  // 设置data-theme属性
  root.setAttribute('data-theme', theme)

  // 同时设置类名，保持与现有样式的兼容性
  if (isDark) {
    root.classList.add('dark-theme')
  } else {
    root.classList.remove('dark-theme')
  }
}

// 为特定组件优先应用主题
const prioritizeComponents = (_theme: ThemeType) => {
  // 选择需要优先更新的组件
  const prioritySelectors = [
    '.search-container',
    '.n-input',
    '.user-info-group',
    '.user-info',
    '.avatar-container',
    '.n-popover',
    '.notification-container',
    // 评论相关组件
    '.comment-info-container',
    '.comment-list-container',
    '.user-comment-container',
    '.user-comment-container-fixed',
    '.comment-content-row',
    '.comment-input-row',
    '.comment-reply-row',
    '.tiptap-editor-wrapper',
    '.editor-content',
    '.ProseMirror',
    '.ProseMirrorInput',
    '.article-content',
  ]

  // 强制刷新特定元素
  const forceRefreshSpecificElements = () => {
    // 评论编辑器特殊处理
    document
      .querySelectorAll('.ProseMirror, .editor-content, .tiptap-editor-wrapper')
      .forEach((el) => {
        if (el instanceof HTMLElement) {
          // 添加特定属性标记需要立即刷新的组件
          el.setAttribute('data-theme-refresh', 'true')
          // 触发重绘
          requestAnimationFrame(() => {
            void el.offsetHeight
            // 应用后移除标记
            setTimeout(() => el.removeAttribute('data-theme-refresh'), 50)
          })
        }
      })
  }

  // 1. 标记所有优先组件
  prioritySelectors.forEach((selector) => {
    const elements = document.querySelectorAll(selector)
    elements.forEach((el) => {
      if (el instanceof HTMLElement) {
        // 标记为优先级组件
        el.classList.add('theme-priority')
      }
    })
  })

  // 2. 触发编辑器的特殊刷新
  forceRefreshSpecificElements()

  // 3. 直接对评论容器执行一次DOM强制重绘
  const commentContainers = document.querySelectorAll(
    '.comment-info-container, .article-info-container',
  )
  commentContainers.forEach((container) => {
    if (container instanceof HTMLElement) {
      // 使用更彻底的方式刷新关键容器
      const oldDisplay = container.style.display
      container.style.display = 'none'
      void container.offsetHeight // 触发重排
      container.style.display = oldDisplay
    }
  })
}

export default {
  activeTheme,
  loadSavedTheme,
  toggleTheme,
  THEME_NAMES,
  isThemeChanging,
}
