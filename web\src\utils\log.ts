import config from '@/config'

const logLevels = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
}

const currentLogLevel = config.logLevel || logLevels.INFO // 默认是 INFO 级别

// 判断日志级别是否满足输出要求
function shouldLog(level: string): boolean {
  const levels = Object.values(logLevels)
  const currentLevelIndex = levels.indexOf(currentLogLevel)
  const targetLevelIndex = levels.indexOf(level)
  return targetLevelIndex >= currentLevelIndex
}

const logger = {
  levels: logLevels,
  debug(message: any, ...args: any[]) {
    if (shouldLog(logLevels.DEBUG)) {
      console.debug(message, ...args)
    }
  },
  info(message: any, ...args: any[]) {
    if (shouldLog(logLevels.INFO)) {
      console.info(message, ...args)
    }
  },
  warn(message: any, ...args: any[]) {
    if (shouldLog(logLevels.WARN)) {
      console.warn(message, ...args)
    }
  },
  error(message: any, ...args: any[]) {
    if (shouldLog(logLevels.ERROR)) {
      console.error(message, ...args)
    }
  },
}

export default logger
