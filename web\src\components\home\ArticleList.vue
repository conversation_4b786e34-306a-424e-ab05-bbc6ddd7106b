<template>
  <div 
    class="article-container" 
    ref="containerRef"
    @touchstart="handleContainerTouchStart"
    @touchend="handleContainerTouchEnd"
    @touchcancel="handleContainerTouchCancel"
  >
    <NInfiniteScroll
      @load="loadMore"
      :distance="100"
      class="infinite-scroll-container"
      ref="scrollContainerRef"
    >
      <NRow
        :gutter="20"
        style="
          width: 100%;
          box-sizing: border-box;
          margin: 0 auto;
          padding: 0 0.25rem;
          flex: 1;
          overflow-y: auto;
        "
      >
        <NCol v-for="(article, index) in articleList" :key="article.id" :span="cardColSpan">
          <ArticleCard
            :article="article"
            :index="index"
            :card-color="getCardColor(article.id, index)"
            :is-dragging="isDragging"
            :dragged-article="draggedArticle || undefined"
            :drag-over-card-id="dragOverCardId || undefined"
            :drag-over-position="dragOverPosition || undefined"
            :is-single-card-row="isSingleCardRow"
            :drag-style="dragStyle"
            @toggle-scope="handleToggleScope"
            @start-long-press="handleStartLongPress"
            @cancel-long-press="cancelLongPress"
            @download="handleDownload"
            @set-editor="handleSetEditor"
          />
        </NCol>
      </NRow>
      <div class="infinite-load-info">
        <NSpin v-if="loading" class="display-flex" />
        <NEmpty v-if="noMore" description="没有更多文章了..." />
      </div>
    </NInfiniteScroll>
    
    <!-- 垃圾篓组件 -->
    <TrashBin :visible="showTrashBin" :is-active="isOverTrashBin" />
  </div>
</template>

<script lang="ts" setup>
import { NRow, NCol, NSpin, NEmpty, NInfiniteScroll } from 'naive-ui'
import { onMounted, watch } from 'vue'

import articleApi from '@/api/article'
import ArticleCard from '@/components/home/<USER>'
import TrashBin from '@/components/home/<USER>'
import { useArticleDrag } from '@/composables/useArticleDrag'
import { useArticleList } from '@/composables/useArticleList'

const props = defineProps({
  searchCondition: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['reset'])

// 使用文章列表组合式函数
const {
  articleList,
  loading,
  noMore,
  cardColSpan,
  articleTiptapEditorMap,
  containerRef,
  scrollContainerRef,
  getCardColor,
  updateColSpan,
  resetList,
  loadArticles,
  handleToggleScope,
  handleDeleteArticle,
  handleReorderArticles,
} = useArticleList(props)

// 使用拖拽组合式函数
const {
  isDragging,
  draggedArticle,
  showTrashBin,
  isOverTrashBin,
  dragStyle,
  dragOverCardId,
  dragOverPosition,
  isSingleCardRow,
  startLongPress,
  cancelLongPress,
  isLongPressActive,
} = useArticleDrag({
  onDragStart: (article) => {
    console.log('开始拖拽文章:', article.title)
  },
  onDelete: handleDeleteArticle,
  onReorder: handleReorderArticles
})

// 监听搜索条件变化，重置列表
watch(
  () => props.searchCondition,
  () => {
    resetList()
  },
  { deep: true },
)

onMounted(() => {
  updateColSpan()
  window.addEventListener('resize', resizeCallback)
})

const resizeCallback = () => {
  updateColSpan()
}

// 加载更多文章
const loadMore = () => {
  loadArticles(true)
}

// 处理开始长按
const handleStartLongPress = (event: MouseEvent | TouchEvent, article: any, avatarElement: HTMLElement) => {
  startLongPress(event, article, avatarElement)
}

// 处理下载
const handleDownload = (id: string) => {
  const editor = articleTiptapEditorMap.value.get(id)
  articleApi.md(id, editor)
}

// 处理设置编辑器
const handleSetEditor = (articleId: string, editor: any) => {
  articleTiptapEditorMap.value.set(articleId, editor)
}

// 暴露方法给父组件
defineExpose({
  loadArticles,
  resetList,
})

const handleContainerTouchStart = () => {
  // 容器触摸开始时不需要特殊处理
}

const handleContainerTouchEnd = () => {
  // 容器触摸结束时不需要特殊处理，拖拽由全局事件处理
}

const handleContainerTouchCancel = () => {
  // 只在触摸被系统取消时处理
  if (isLongPressActive) {
    console.log('容器触摸取消 - 取消长按检测')
    cancelLongPress()
  }
}
</script>

<style scoped lang="scss">
.article-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: hidden;

  .infinite-scroll-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .infinite-load-info {
    width: 100%;
    padding: 1.25rem 0;
    text-align: center;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
