<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.shenmo.wen</groupId>
    <artifactId>wen-modules</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>modules</name>
    <modules>
        <module>wen-user</module>
    </modules>

    <properties>
        <version>1.0.0-SNAPSHOT</version>
        <java.version>17</java.version>
        <character.encoding>UTF-8</character.encoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>${character.encoding}</project.build.sourceEncoding>
        <project.reporting.outputEncoding>${character.encoding}</project.reporting.outputEncoding>
        <deploy-plugin.version>3.1.3</deploy-plugin.version>
        <spring-boot.version>3.4.3</spring-boot.version>
        <wen-common.version>${version}</wen-common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common</artifactId>
                <version>${wen-common.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-user</artifactId>
                <version>${version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
