<template>
  <div class="image-cursor-test">
    <h2>图片光标和控制点测试</h2>
    
    <div class="test-section">
      <h3>测试说明</h3>
      <ul>
        <li>1. 点击图片应该能正确选中，控制点应该精确对齐图片边缘中心</li>
        <li>2. 点击图片所在行的空白处，光标应该稳定定位，不应该闪烁</li>
        <li>3. 光标与图片之间应该有适当间距，不应该重叠</li>
        <li>4. 不应该看到 ProseMirror-separator 或 ProseMirror-trailingBreak 元素</li>
      </ul>
    </div>

    <div class="editor-container">
      <TiptapEditor
        v-model="content"
        :editable="true"
        placeholder="在这里测试图片功能..."
        class="test-editor"
      />
    </div>

    <div class="test-controls">
      <button @click="insertTestImage" class="test-button">
        插入测试图片
      </button>
      <button @click="toggleEditMode" class="test-button">
        切换编辑模式: {{ editable ? '编辑' : '只读' }}
      </button>
      <button @click="inspectDOM" class="test-button">
        检查DOM结构
      </button>
    </div>

    <div class="debug-info" v-if="debugInfo">
      <h3>调试信息</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import TiptapEditor from '@/components/tiptap/TiptapEditor.vue'

// 响应式数据
const content = ref(`
<p>这是一个测试段落，用于测试图片光标定位。</p>
<p>请在这里插入图片，然后测试以下功能：</p>
<p>1. 点击图片查看控制点是否精确对齐</p>
<p>2. 点击图片所在行的空白处，观察光标是否稳定</p>
<p>3. 检查光标与图片的间距是否合适</p>
`)

const editable = ref(true)
const debugInfo = ref('')

// 插入测试图片
const insertTestImage = () => {
  // 创建一个简单的测试图片 URL
  const testImageUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPua1i+ivleWbvueJhzwvdGV4dD4KPC9zdmc+'
  
  // 在当前内容后添加图片
  content.value += `<p>测试图片：<img src="${testImageUrl}" alt="测试图片" width="200" height="100" /> 图片后的文本</p>`
}

// 切换编辑模式
const toggleEditMode = () => {
  editable.value = !editable.value
}

// 检查DOM结构
const inspectDOM = () => {
  const editorElement = document.querySelector('.test-editor .ProseMirror')
  if (editorElement) {
    const info = {
      separatorElements: editorElement.querySelectorAll('.ProseMirror-separator').length,
      trailingBreakElements: editorElement.querySelectorAll('.ProseMirror-trailingBreak').length,
      imageWrappers: editorElement.querySelectorAll('.resizable-image-wrapper').length,
      resizeHandles: editorElement.querySelectorAll('.resize-handle').length,
      selectedImages: editorElement.querySelectorAll('.resizable-image-wrapper.ProseMirror-selectednode').length,
    }
    
    debugInfo.value = JSON.stringify(info, null, 2)
    
    // 输出到控制台以便详细检查
    console.log('DOM 检查结果:', info)
    console.log('编辑器元素:', editorElement)
    
    // 检查是否有隐藏的分隔符元素
    const separators = editorElement.querySelectorAll('.ProseMirror-separator')
    const trailingBreaks = editorElement.querySelectorAll('.ProseMirror-trailingBreak')
    
    console.log('分隔符元素:', separators)
    console.log('尾随换行元素:', trailingBreaks)
    
    // 检查图片包装器的样式
    const imageWrappers = editorElement.querySelectorAll('.resizable-image-wrapper')
    imageWrappers.forEach((wrapper, index) => {
      const rect = wrapper.getBoundingClientRect()
      const img = wrapper.querySelector('img')
      const imgRect = img?.getBoundingClientRect()
      
      console.log(`图片包装器 ${index + 1}:`, {
        wrapper: { width: rect.width, height: rect.height },
        image: imgRect ? { width: imgRect.width, height: imgRect.height } : null,
        handles: wrapper.querySelectorAll('.resize-handle').length
      })
    })
  }
}
</script>

<style scoped>
.image-cursor-test {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #2d8cf0;
}

.test-section ul {
  margin: 0;
  padding-left: 1.5rem;
}

.test-section li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.editor-container {
  margin: 2rem 0;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.test-editor {
  min-height: 300px;
}

.test-controls {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.test-button {
  padding: 0.5rem 1rem;
  background-color: #2d8cf0;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.test-button:hover {
  background-color: #1c7ed6;
}

.debug-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f1f3f4;
  border-radius: 8px;
  border-left: 4px solid #2d8cf0;
}

.debug-info h3 {
  margin-top: 0;
  color: #2d8cf0;
}

.debug-info pre {
  background-color: #fff;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>
