import { addImageZoomWheelListener } from '../../../events/PassiveEventHandlers'
import { getFullImageUrl } from '../ImageResourceManager'

/**
 * 图片预览配置接口
 */
export interface ImagePreviewOptions {
  src: string
  originalSrc: string
  alt: string
  useThumbnail: boolean
  onOriginalLoaded?: (originalUrl: string) => void
}

/**
 * 图片预览组合式函数
 * 处理图片预览模态框的显示、缩放、关闭等功能
 */
export function useImagePreview() {
  /**
   * 显示图片预览模态框
   */
  const showImagePreview = (options: ImagePreviewOptions) => {
    const { src, originalSrc, alt, onOriginalLoaded } = options

    // 创建模态框
    const modal = document.createElement('div')
    modal.classList.add('modal-overlay')

    // 创建预览图
    const previewImg = document.createElement('img')
    previewImg.alt = alt
    modal.appendChild(previewImg)
    document.body.appendChild(modal)

    // 触发模态框显示动画
    setTimeout(() => {
      modal.classList.add('modal-overlay-active')
    }, 10)

    // 检查图片是否已经是原图
    const isAlreadyOriginal =
      !originalSrc.includes('/thumbnail') || src.indexOf('/thumbnail') === -1

    if (originalSrc.includes('/thumbnail') && !isAlreadyOriginal) {
      // 处理缩略图预览
      previewImg.src = src
      previewImg.style.opacity = '0.5'

      // 添加加载动画
      const loadingSpinner = document.createElement('div')
      loadingSpinner.classList.add('loading-spinner')
      modal.appendChild(loadingSpinner)

      // 加载原图
      const originalImg = new Image()
      const animationStartTime = Date.now()
      const minDisplayTime = 500

      originalImg.onload = () => {
        const loadTime = Date.now() - animationStartTime

        const setOriginal = () => {
          loadingSpinner.style.display = 'none'
          previewImg.src = originalImg.src
          previewImg.style.opacity = '1'

          // 将原图URL保存到图片元素，以便关闭模态框后更新
          previewImg.dataset.originalFullUrl = originalImg.src
        }

        if (loadTime < minDisplayTime) {
          setTimeout(setOriginal, minDisplayTime - loadTime)
        } else {
          setOriginal()
        }
      }

      // 获取原图URL
      const originalFullUrl = getFullImageUrl(originalSrc.replace('/thumbnail', ''), false)
      originalImg.src = originalFullUrl
    } else {
      // 已经是原图，直接显示
      previewImg.src = src
      previewImg.style.opacity = '1'
    }

    // 添加滚轮缩放功能
    const { setupZoomControls, cleanupZoomControls } = setupImageZoom(previewImg)
    const removeWheelListener = addImageZoomWheelListener(modal, setupZoomControls)

    // 关闭模态框函数
    const closeModal = () => {
      modal.classList.remove('modal-overlay-active')
      modal.addEventListener(
        'transitionend',
        () => {
          if (!modal.classList.contains('modal-overlay-active')) {
            // 如果已经加载了原图，则调用回调函数
            if (previewImg.dataset.originalFullUrl && originalSrc.includes('/thumbnail')) {
              onOriginalLoaded?.(previewImg.dataset.originalFullUrl)
            }

            // 清理事件监听器
            document.body.removeChild(modal)
            document.removeEventListener('keydown', handleEscKey)
            removeWheelListener()
            cleanupZoomControls()
          }
        },
        { once: true },
      )
    }

    // 点击关闭
    modal.addEventListener('click', closeModal, { once: true })

    // ESC关闭
    const handleEscKey = (e: KeyboardEvent): void => {
      if (e.key === 'Escape') {
        closeModal()
      }
    }
    document.addEventListener('keydown', handleEscKey)
  }

  return {
    showImagePreview,
  }
}

/**
 * 设置图片缩放控制
 */
function setupImageZoom(previewImg: HTMLImageElement) {
  let scale = 1
  const minScale = 0.5
  const maxScale = 3
  const scaleStep = 0.1

  const setupZoomControls = (e: WheelEvent): void => {
    // 阻止默认滚动行为
    e.preventDefault()

    // 计算缩放方向
    const delta = e.deltaY > 0 ? -scaleStep : scaleStep
    const newScale = Math.max(minScale, Math.min(maxScale, scale + delta))

    if (newScale !== scale) {
      scale = newScale
      previewImg.style.transform = `scale(${scale})`
    }
  }

  const cleanupZoomControls = () => {
    scale = 1
    previewImg.style.transform = ''
  }

  return {
    setupZoomControls,
    cleanupZoomControls,
  }
}
