import image, { createImageExtension } from '@/components/tiptap/extensions/image/ImageExtension'
import { createNodeView } from '@/components/tiptap/extensions/image/ImageNodeView'
import { handleImageUpload } from '@/components/tiptap/extensions/image/ImageResourceManager'
import { useImageUpload } from '@/components/tiptap/extensions/image/useImageUpload'

export { createImageExtension, handleImageUpload, createNodeView, useImageUpload }
export default image
