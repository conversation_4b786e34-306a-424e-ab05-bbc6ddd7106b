import {
  extractRelativePath,
  getFullImageUrl,
} from '@/components/tiptap/extensions/image/ImageResourceManager'

import { ImagePreviewHandler } from './nodeview/ImagePreviewHandler'
import { ImageResizeController } from './nodeview/ImageResizeController'
import { ResizeHandleManager } from './nodeview/ResizeHandleManager'

import type { NodeViewRendererProps } from '@tiptap/core'

/**
 * 创建图片节点视图
 * @param useThumbnail 是否使用缩略图
 * @returns 节点视图渲染函数
 */
export const createNodeView = (useThumbnail: boolean) => {
  return ({ node, editor, getPos }: NodeViewRendererProps) => {
    // 检测是否为移动设备
    const isMobileDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    // 创建可调整大小的包装器
    const resizableWrapper = document.createElement('div')
    resizableWrapper.classList.add('resizable-image-wrapper')
    resizableWrapper.contentEditable = 'false' // 防止内容被编辑
    resizableWrapper.style.display = 'inline-flex' // 使用inline-flex提高稳定性
    resizableWrapper.style.alignItems = 'center' // 居中对齐内容
    resizableWrapper.style.justifyContent = 'center' // 水平居中
    resizableWrapper.style.position = 'relative' // 确保相对定位
    resizableWrapper.style.margin = '0' // 移除外边距
    resizableWrapper.style.padding = '0' // 移除内边距
    resizableWrapper.style.verticalAlign = 'baseline' // 使用基线对齐，避免中线对齐导致的抖动
    resizableWrapper.style.lineHeight = '1' // 固定行高为1，避免行高变化
    resizableWrapper.style.transform = 'translateY(0)' // 强制位置稳定
    resizableWrapper.style.transition = 'none' // 禁用可能的过渡动画
    resizableWrapper.style.zIndex = '1' // 提高控制优先级
    resizableWrapper.style.willChange = 'transform' // 提示浏览器这个元素会变化，优化渲染
    resizableWrapper.style.whiteSpace = 'nowrap' // 防止换行导致的重排

    // 设置默认样式
    resizableWrapper.style.boxSizing = 'border-box'
    resizableWrapper.style.width = 'fit-content' // 使用fit-content使宽度适应内容
    resizableWrapper.style.maxWidth = '100%'
    // 添加额外样式确保边框不会影响内部布局
    resizableWrapper.style.border = '0' // 默认无边框

    // 只在非编辑模式下添加额外样式
    if (!editor.isEditable) {
      resizableWrapper.classList.add('readonly-image')
      // 在非编辑模式下，移除所有选中状态
      resizableWrapper.classList.remove('ProseMirror-selectednode')
    }

    // 监听编辑模式变化
    editor.on('update', () => {
      if (!editor.isEditable) {
        // 非编辑模式下移除选中状态
        resizableWrapper.classList.remove('ProseMirror-selectednode')
      } else if (resizableWrapper.classList.contains('ProseMirror-selectednode')) {
        // 在编辑模式下，如果节点被选中，确保控制点可见
        handleManager.showAllHandles()
      }
    })

    // 先清空所有内容，避免重复添加元素
    resizableWrapper.innerHTML = ''

    // 创建图片元素
    const img = document.createElement('img')

    // 声明DOM观察器变量
    let mutationObserver: MutationObserver | null = null

    // 获取相对路径并存储
    const relativeSrc = extractRelativePath(node.attrs.src)
    img.dataset.relativeSrc = relativeSrc

    // 设置图片显示用的完整URL
    img.src = getFullImageUrl(relativeSrc, useThumbnail)
    img.alt = node.attrs.alt || ''
    img.classList.add('cursor-pointer')
    img.contentEditable = 'false' // 防止图片被编辑
    img.style.display = 'block' // 确保图片在flex容器中是块级元素
    img.style.maxWidth = '100%' // 最大宽度限制
    img.style.margin = '0' // 确保没有默认的外边距
    img.style.padding = '0' // 确保没有内边距
    img.style.verticalAlign = 'baseline' // 使用基线对齐
    img.style.transform = 'translateY(0)' // 防止抖动
    img.style.transition = 'none' // 禁用过渡
    img.style.willChange = 'transform' // 提示浏览器这个元素会变化，优化渲染
    img.loading = 'eager' // 禁用延迟加载，使用即时加载

    // 添加原始路径数据属性，用于图片预览
    img.dataset.originalSrc = relativeSrc

    // 应用宽高属性
    if (node.attrs.width) {
      img.style.width = node.attrs.width
    }
    if (node.attrs.height) {
      img.style.height = node.attrs.height
    }

    // 创建图片预览处理器
    const previewHandler = new ImagePreviewHandler(img, editor, getPos)

    // 根据编辑模式添加不同的事件监听
    if (editor.isEditable) {
      // 编辑模式下使用双击预览
      img.addEventListener('dblclick', previewHandler.handlePreview)
    } else {
      // 非编辑模式下使用单击预览
      img.addEventListener('click', previewHandler.handlePreview)
    }

    // 将图片添加到包装器
    resizableWrapper.appendChild(img)

    // 创建调整大小管理器
    const handleManager = new ResizeHandleManager(resizableWrapper, isMobileDevice)
    const handles = handleManager.setupResizeHandles()

    // 创建调整大小控制器
    const resizeController = new ImageResizeController(
      img,
      resizableWrapper,
      editor,
      getPos,
      handles,
      isMobileDevice,
    )

    // 为控制点添加事件监听
    const addHandleListeners = (handle: HTMLElement) => {
      // 添加鼠标事件监听
      handle.addEventListener('mousedown', (e) => resizeController.handleResizeStart(e, handle))

      // 添加触摸事件支持
      handle.addEventListener('touchstart', (e) => resizeController.handleTouchStart(e, handle), {
        passive: true,
      })
    }

    // 为所有控制点添加事件监听
    Object.values(handles).forEach((handle) => {
      if (handle) addHandleListeners(handle)
    })

    // 创建并初始化 MutationObserver - 优化性能
    const initMutationObserver = () => {
      if (mutationObserver) {
        mutationObserver.disconnect()
      }

      // 使用防抖减少频繁的DOM操作
      let observerTimer: number | null = null

      mutationObserver = new MutationObserver((mutations) => {
        // 清除之前的定时器
        if (observerTimer) {
          clearTimeout(observerTimer)
        }

        // 使用防抖处理变化
        observerTimer = window.setTimeout(() => {
          // 只处理类名变化
          const relevantMutations = mutations.filter(
            (mutation) => mutation.type === 'attributes' && mutation.attributeName === 'class',
          )

          if (relevantMutations.length === 0) {
            return
          }

          const target = relevantMutations[0].target as HTMLElement
          const isSelected = target.classList.contains('ProseMirror-selectednode')

          // 批量更新控制点状态
          const handleElements = Object.values(handles).filter(Boolean)

          if (editor.isEditable && isSelected) {
            // 编辑模式下选中时显示控制点
            handleElements.forEach((handle) => {
              if (handle) {
                handle.style.display = 'block'
                handle.style.visibility = 'visible'
                handle.style.opacity = '1'
              }
            })
          } else {
            // 其他情况隐藏控制点
            handleElements.forEach((handle) => {
              if (handle) {
                handle.style.display = 'none'
                handle.style.visibility = 'hidden'
                handle.style.opacity = '0'
              }
            })

            // 非编辑模式下移除选中样式
            if (!editor.isEditable && isSelected) {
              requestAnimationFrame(() => {
                target.classList.remove('ProseMirror-selectednode')
              })
            }
          }
        }, 50) // 50ms防抖
      })

      // 开始观察，只观察类名变化
      mutationObserver.observe(resizableWrapper, {
        attributes: true,
        attributeFilter: ['class'],
        subtree: false, // 不观察子树，减少性能影响
      })
    }

    // 初始化 MutationObserver
    initMutationObserver()

    // 确保在非编辑模式下立即移除选中样式
    if (!editor.isEditable) {
      // 移除选中样式
      resizableWrapper.classList.remove('ProseMirror-selectednode')
      // 隐藏所有控制点
      handleManager.hideAllHandles()
    }

    const update = (updatedNode: {
      attrs: { src: string; alt?: string; width?: string; height?: string }
    }) => {
      if (updatedNode.attrs.src !== node.attrs.src) {
        const relativeSrc = extractRelativePath(updatedNode.attrs.src)
        img.dataset.relativeSrc = relativeSrc
        img.src = getFullImageUrl(relativeSrc, useThumbnail)
      }
      if (updatedNode.attrs.alt !== node.attrs.alt) {
        img.alt = updatedNode.attrs.alt || ''
      }
      if (updatedNode.attrs.width !== node.attrs.width) {
        img.style.width = updatedNode.attrs.width || ''
      }
      if (updatedNode.attrs.height !== node.attrs.height) {
        img.style.height = updatedNode.attrs.height || ''
      }
    }

    // 返回节点视图对象
    return {
      dom: resizableWrapper,
      contentDOM: null,
      ignoreMutation: () => true,
      update,
      destroy: () => {
        // 清理DOM观察器
        if (mutationObserver) {
          mutationObserver.disconnect()
          mutationObserver = null
        }

        // 移除编辑器事件监听
        editor.off('update')
      },
    }
  }
}
