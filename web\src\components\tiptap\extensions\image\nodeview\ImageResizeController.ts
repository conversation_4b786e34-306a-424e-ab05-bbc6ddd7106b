interface EditorInstance {
  isEditable: boolean
  commands: {
    setNodeSelection: (pos: number) => void
    updateAttributes: (type: string, attrs: Record<string, unknown>) => void
  }
}

/**
 * 图片调整大小控制器
 * 处理图片的拖拽调整大小功能
 */
export class ImageResizeController {
  private startX = 0
  private startY = 0
  private startWidth = 0
  private startHeight = 0
  private currentHandle: HTMLElement | null = null
  private aspectRatio = 1
  private isResizing = false

  // 尺寸信息显示元素
  private resizeInfo: HTMLDivElement

  constructor(
    private img: HTMLImageElement,
    private resizableWrapper: HTMLDivElement,
    private editor: EditorInstance,
    private getPos: () => number,
    private handles: Record<string, HTMLElement | null>,
    private isMobileDevice: boolean,
  ) {
    this.resizeInfo = this.createResizeInfo()
    this.resizableWrapper.appendChild(this.resizeInfo)
  }

  /**
   * 创建尺寸信息显示元素
   */
  private createResizeInfo(): HTMLDivElement {
    const resizeInfo = document.createElement('div')
    resizeInfo.classList.add('resize-info')
    resizeInfo.style.display = 'none'
    resizeInfo.style.position = 'absolute'
    resizeInfo.style.top = '50%'
    resizeInfo.style.left = '50%'
    resizeInfo.style.transform = 'translate(-50%, -50%)'
    resizeInfo.style.backgroundColor = 'rgba(0, 0, 0, 0.8)'
    resizeInfo.style.color = 'white'
    resizeInfo.style.padding = '4px 10px'
    resizeInfo.style.borderRadius = '4px'
    resizeInfo.style.fontSize = '12px'
    resizeInfo.style.fontWeight = 'bold'
    resizeInfo.style.pointerEvents = 'none'
    resizeInfo.style.whiteSpace = 'nowrap'
    resizeInfo.style.zIndex = '150'
    resizeInfo.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)'

    // 为移动设备应用特定样式
    if (this.isMobileDevice) {
      resizeInfo.style.padding = '5px 12px'
      resizeInfo.style.fontSize = '14px'
      resizeInfo.style.boxShadow = '0 3px 10px rgba(0, 0, 0, 0.4)'
    }

    return resizeInfo
  }

  /**
   * 开始调整大小
   */
  handleResizeStart = (e: MouseEvent, handle: HTMLElement) => {
    // 阻止事件传播，防止编辑器处理这个事件
    e.preventDefault()
    e.stopPropagation()

    // 检查编辑模式和位置函数
    if (!this.editor.isEditable) return
    if (typeof this.getPos !== 'function') return

    // 确保图片节点被选中
    const pos = this.getPos()
    this.editor.commands.setNodeSelection(pos)

    // 记录开始调整的状态
    this.isResizing = true
    this.currentHandle = handle

    // 获取图片当前尺寸
    const rect = this.img.getBoundingClientRect()
    this.startWidth = rect.width
    this.startHeight = rect.height
    this.aspectRatio = this.startWidth / this.startHeight

    // 记录鼠标起始位置
    this.startX = e.clientX
    this.startY = e.clientY

    // 显示尺寸信息
    this.showResizeInfo()

    // 添加全局事件监听
    document.addEventListener('mousemove', this.handleResize)
    document.addEventListener('mouseup', this.handleResizeEnd)
  }

  /**
   * 处理触摸开始
   */
  handleTouchStart = (e: TouchEvent, handle: HTMLElement) => {
    if (!e.touches || e.touches.length === 0) return

    const touch = e.touches[0]

    // 创建类似鼠标事件的对象
    const touchEvent = {
      clientX: touch.clientX,
      clientY: touch.clientY,
      preventDefault: () => {},
      stopPropagation: () => {},
    } as unknown as MouseEvent

    this.handleResizeStart(touchEvent, handle)

    // 在下一帧阻止滚动
    requestAnimationFrame(() => {
      document.body.style.overflow = 'hidden'
    })

    // 添加触摸事件监听
    document.addEventListener('touchmove', this.handleTouchResize, { passive: true })
    document.addEventListener('touchend', this.handleTouchEnd)
    document.addEventListener('touchcancel', this.handleTouchEnd)
  }

  /**
   * 处理调整大小
   */
  private handleResize = (e: MouseEvent) => {
    if (!this.isResizing || !this.currentHandle) return

    e.preventDefault()

    // 计算鼠标移动距离
    const deltaX = e.clientX - this.startX
    const deltaY = e.clientY - this.startY

    // 获取控制点位置
    const position = this.currentHandle.getAttribute('data-handle-position')
    if (!position) return

    // 计算新的宽度和高度
    const { newWidth, newHeight } = this.calculateNewSize(
      position,
      deltaX,
      deltaY,
      e.shiftKey,
      e.altKey,
    )

    // 应用新尺寸
    this.img.style.width = `${newWidth}px`
    this.img.style.height = `${newHeight}px`

    // 更新尺寸信息显示
    this.updateResizeInfo(newWidth, newHeight)
  }

  /**
   * 计算新的尺寸
   */
  private calculateNewSize(
    position: string,
    deltaX: number,
    deltaY: number,
    keepRatio: boolean,
    freeTransform: boolean,
  ): { newWidth: number; newHeight: number } {
    let newWidth = this.startWidth
    let newHeight = this.startHeight

    // 根据控制点位置调整尺寸
    switch (position) {
      case 'top-left':
        newWidth = this.startWidth - deltaX
        newHeight = this.startHeight - deltaY
        break
      case 'top-right':
        newWidth = this.startWidth + deltaX
        newHeight = this.startHeight - deltaY
        break
      case 'bottom-left':
        newWidth = this.startWidth - deltaX
        newHeight = this.startHeight + deltaY
        break
      case 'bottom-right':
        newWidth = this.startWidth + deltaX
        newHeight = this.startHeight + deltaY
        break
      case 'top':
        newHeight = this.startHeight - deltaY
        if (keepRatio) newWidth = newHeight * this.aspectRatio
        break
      case 'right':
        newWidth = this.startWidth + deltaX
        if (keepRatio) newHeight = newWidth / this.aspectRatio
        break
      case 'bottom':
        newHeight = this.startHeight + deltaY
        if (keepRatio) newWidth = newHeight * this.aspectRatio
        break
      case 'left':
        newWidth = this.startWidth - deltaX
        if (keepRatio) newHeight = newWidth / this.aspectRatio
        break
    }

    // 如果按住Shift键且不是中间控制点，保持宽高比
    if (keepRatio && !this.isCenterHandle(position) && !freeTransform) {
      if (
        position === 'top-left' ||
        position === 'top-right' ||
        position === 'bottom-left' ||
        position === 'bottom-right'
      ) {
        // 对于角控制点，使用移动距离较大的方向作为基准
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          newHeight = newWidth / this.aspectRatio
        } else {
          newWidth = newHeight * this.aspectRatio
        }
      }
    }

    // 确保尺寸不小于最小值
    const minSize = 20
    newWidth = Math.max(newWidth, minSize)
    newHeight = Math.max(newHeight, minSize)

    return { newWidth, newHeight }
  }

  /**
   * 判断是否为中心控制点
   */
  private isCenterHandle(position: string): boolean {
    return ['top', 'right', 'bottom', 'left'].includes(position)
  }

  /**
   * 处理触摸调整大小
   */
  private handleTouchResize = (e: TouchEvent) => {
    if (!this.isResizing || !this.currentHandle) return
    if (!e.touches || e.touches.length === 0) return

    const touch = e.touches[0]
    const touchCount = e.touches.length

    // 创建类似鼠标事件的对象
    const touchEvent = {
      clientX: touch.clientX,
      clientY: touch.clientY,
      preventDefault: () => {},
      // 两指触摸模拟按住Alt键（自由调整）
      altKey: touchCount === 2,
      // 三指触摸模拟按住Shift键（保持比例）
      shiftKey: touchCount === 3,
    } as unknown as MouseEvent

    this.handleResize(touchEvent)
  }

  /**
   * 处理触摸结束
   */
  private handleTouchEnd = () => {
    this.handleResizeEnd()
  }

  /**
   * 结束调整大小
   */
  private handleResizeEnd = () => {
    if (!this.isResizing) return

    // 移除全局事件监听
    document.removeEventListener('mousemove', this.handleResize)
    document.removeEventListener('mouseup', this.handleResizeEnd)
    document.removeEventListener('touchmove', this.handleTouchResize)
    document.removeEventListener('touchend', this.handleTouchEnd)
    document.removeEventListener('touchcancel', this.handleTouchEnd)

    // 恢复页面滚动
    document.body.style.overflow = ''

    // 隐藏尺寸信息
    this.hideResizeInfo()

    // 重置调整状态
    this.isResizing = false
    this.currentHandle = null

    // 更新节点属性
    if (typeof this.getPos === 'function') {
      const width = Math.round(parseFloat(this.img.style.width))
      const height = Math.round(parseFloat(this.img.style.height))

      this.editor.commands.updateAttributes('image', {
        width: `${width}px`,
        height: `${height}px`,
      })
    }
  }

  /**
   * 显示尺寸信息
   */
  private showResizeInfo() {
    this.resizeInfo.style.display = 'block'
  }

  /**
   * 隐藏尺寸信息
   */
  private hideResizeInfo() {
    this.resizeInfo.style.display = 'none'
  }

  /**
   * 更新尺寸信息显示
   */
  private updateResizeInfo(width: number, height: number) {
    this.resizeInfo.textContent = `${Math.round(width)} × ${Math.round(height)}`
  }

  /**
   * 获取是否正在调整大小
   */
  getIsResizing(): boolean {
    return this.isResizing
  }
}
