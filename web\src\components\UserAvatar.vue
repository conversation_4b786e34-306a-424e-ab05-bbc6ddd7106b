<template>
  <div class="avatar-container">
    <NPopover trigger="click" placement="bottom">
      <div class="user-info">
        <div class="info-row">
          <strong>手机号：</strong><span>{{ loginUser.phone }}</span>
        </div>
        <div class="info-row">
          <strong>用户名：</strong><span>{{ loginUser.username }}</span>
        </div>
        <div class="info-row">
          <strong>职业：</strong><span>{{ loginUser.job }}</span>
        </div>
        <div class="actions-row">
          <ThemeToggle />
          <NButton type="error" size="tiny" @click="logout">退出登录</NButton>
        </div>
      </div>
      <template #trigger>
        <LongPress @long-press="handleAvatarFileClick">
          <NAvatar :size="56" :src="loginUser.avatar" object-fit="cover" class="cursor-pointer" />
        </LongPress>
      </template>
    </NPopover>
    <input
      type="file"
      ref="avatarFileInputRef"
      @change="handleAvatarFileChange"
      class="display-none"
    />
  </div>
</template>

<script lang="tsx" setup>
import { NPopover, NButton, NAvatar } from 'naive-ui'
import { ref, onMounted } from 'vue'

import authenticationApi from '@/api/authentication'
import fileApi from '@/api/file'
import userApi from '@/api/user'
import LongPress from '@/components/LongPress.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import { HOME_CARD, HOME_SEARCH_CONDITION } from '@/constants/storage.constants'
import router from '@/router'
import { type ResponseData } from '@/types/response_data.types'
import { type LoginUser } from '@/types/user.types'
import localStorage from '@/utils/local-storage'

const loginUser = ref<LoginUser>({} as LoginUser)
const avatarFileInputRef = ref<HTMLInputElement | null>(null)

onMounted(() => {
  // 获取最新的用户信息
  userApi.info().then((res: ResponseData) => {
    if (res.data) {
      loginUser.value = res.data
      loginUser.value.avatar = getResourceURL(loginUser.value.avatar)
      // 更新本地存储的用户信息
      localStorage.setLoginUser(res.data)
    }
  })
})

// 头像点击事件，触发上传
const handleAvatarFileClick = () => {
  avatarFileInputRef.value?.click()
}
const getResourceURL = (uri: string): string => {
  return fileApi.getResourceURL(uri)
}
// 处理头像文件上传
const handleAvatarFileChange = async (e: Event): Promise<void> => {
  const target = e.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    userApi.changeAvatar(file).then((res: ResponseData) => {
      if (res.data) {
        loginUser.value.avatar = getResourceURL(res.data)
        userApi.info().then((res: ResponseData) => {
          localStorage.setLoginUser(res.data)
        })
      }
    })
  }
}
// 退出登录的函数
const logout = () => {
  authenticationApi.logout().then(() => {
    router.push('/login')
    localStorage.removeLoginUser()
    // 清除搜索条件
    localStorage.remove(HOME_SEARCH_CONDITION)
    // 清除卡片显示状态，确保下次登录默认显示文章
    localStorage.remove(HOME_CARD)
  })
}
</script>

<style scoped>
/* 头像容器样式，使用 flex 布局实现内部元素的居中对齐 */
.avatar-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 用户信息文本样式 */
.user-info {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  min-width: 8rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.info-row strong {
  margin-right: 0.3rem;
  min-width: 3.5rem;
  flex-shrink: 0;
}

/* 操作按钮行 */
.actions-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 10%);
}
</style>
