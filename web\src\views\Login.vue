<template>
  <div class="layout-container">
    <BackgroundAnimation :particleCount="40" />
    <div class="header-container">
      <NImage 
        width="200" 
        preview-disabled 
        :src="logo" 
        :loading="true"
        :fallback-src="logo"
      />
    </div>
    <div class="card-container">
      <NCard hoverable :style="cardStyle">
        <!-- 登录表单 -->
        <NForm
          v-show="!isFlipped"
          label-placement="left"
          :model="loginForm"
          :rules="loginFormRules"
          ref="loginFormRef"
          :label-width="80"
          class="login-form"
        >
          <NFormItem label="账号" path="phone">
            <NInput
              class="login-form-ipt"
              maxlength="11"
              v-model:value="loginForm.phone"
              @input="loginInputNum"
              placeholder="请输入手机号"
              @keyup.enter="handleLogin"
            />
          </NFormItem>
          <NFormItem label="密码" path="password">
            <NInput
              class="login-form-ipt"
              minlength="6"
              v-model:value="loginForm.password"
              type="password"
              placeholder="请输入密码"
              show-password-on="click"
              @keyup.enter="handleLogin"
            />
          </NFormItem>
          <!-- 按钮容器 -->
          <div class="login-form-btn">
            <!-- 登录按钮 -->
            <NButton class="login-btn" type="info" @click="handleLogin">登录</NButton>
            <!-- 切换按钮 -->
            <NButton class="flip-btn" @click="flipCard">注册</NButton>
          </div>
        </NForm>
        <!-- 注册表单 -->
        <NForm
          v-show="isFlipped"
          label-placement="left"
          :model="registerForm"
          :rules="registerFormRules"
          ref="registerFormRef"
          :label-width="80"
          class="register-form"
        >
          <NFormItem label="用户名" path="username">
            <NInput
              class="register-form-ipt"
              v-model:value="registerForm.username"
              placeholder="请输入用户名"
              @keyup.enter="handleRegister"
            />
          </NFormItem>
          <NFormItem label="手机号" path="phone">
            <NInput
              class="register-form-ipt"
              maxlength="11"
              v-model:value="registerForm.phone"
              @input="registerInputNum"
              placeholder="请输入手机号"
              @keyup.enter="handleRegister"
            />
          </NFormItem>
          <NFormItem label="密码" path="password">
            <NInput
              class="register-form-ipt"
              v-model:value="registerForm.password"
              type="password"
              placeholder="请输入密码"
              show-password-on="click"
              @keyup.enter="handleRegister"
            />
          </NFormItem>
          <NFormItem label="确认密码" path="reenteredPassword">
            <NInput
              class="register-form-ipt"
              v-model:value="registerForm.reenteredPassword"
              type="password"
              :disabled="!registerForm.password"
              placeholder="请确认密码"
              show-password-on="click"
              @keyup.enter="handleRegister"
            />
          </NFormItem>
          <NFormItem label="职业" path="job">
            <NInput
              class="register-form-ipt"
              v-model:value="registerForm.job"
              placeholder="请输入职业"
              @keyup.enter="handleRegister"
            />
          </NFormItem>
          <!-- 按钮容器 -->
          <div class="register-form-btn">
            <!-- 注册按钮 -->
            <NButton class="login-btn" type="info" @click="handleRegister">注册并登录</NButton>
            <!-- 切换按钮 -->
            <NButton class="flip-btn" @click="flipCard">登录</NButton>
          </div>
        </NForm>
      </NCard>
    </div>
    <div class="footer-container">
      <ThemeToggle />
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { NCard, NForm, NFormItem, NButton, NInput, NImage } from 'naive-ui'
import { ref, computed } from 'vue'

import authenticationApi from '@/api/authentication'
import logo from '@/assets/logo.png'
import BackgroundAnimation from '@/components/BackgroundAnimation.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import { HOME_CARD } from '@/constants/storage.constants'
import router from '@/router'
import type { ResponseData } from '@/types/response_data.types'
import localStorage from '@/utils/local-storage'

import type { FormItemRule } from 'naive-ui'

// 控制卡片翻转
const isFlipped = ref(false)
// 翻转卡片
const flipCard = () => {
  // 切换表单
  isFlipped.value = !isFlipped.value
}
// 登录表单
const loginFormRef = ref()
const loginForm = ref({
  phone: '',
  password: '',
})
const loginFormRules = {
  phone: [
    { required: true, message: '手机号不能为空', trigger: 'blur' },
    {
      // 正则校验
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入有效的手机号',
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少需要6个字符', trigger: 'blur' },
  ],
}
// 注册表单
const registerFormRef = ref()
const registerForm = ref({
  username: '',
  phone: '',
  password: '',
  reenteredPassword: '',
  job: '',
})
const validatePasswordStartWith = (rule: FormItemRule, value: string): boolean => {
  return (
    !!registerForm.value.password &&
    registerForm.value.password.startsWith(value) &&
    registerForm.value.password.length >= value.length
  )
}
const validatePasswordSame = (rule: FormItemRule, value: string): boolean => {
  return value === registerForm.value.password
}
const registerFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 10, message: '用户名长度在 3 到 10 个字符之间', trigger: 'blur' },
  ],
  ...loginFormRules,
  reenteredPassword: [
    {
      required: true,
      message: '请再次输入密码',
      trigger: ['input', 'blur'],
    },
    {
      validator: validatePasswordStartWith,
      message: '两次密码输入不一致',
      trigger: 'input',
    },
    {
      validator: validatePasswordSame,
      message: '两次密码输入不一致',
      trigger: ['blur', 'password-input'],
    },
  ],
  job: [
    { required: true, message: '请输入职业', trigger: 'blur' },
    { min: 2, max: 8, message: '职业名长度在 2 到 8 个字符之间', trigger: 'blur' },
  ],
}
// 登录操作
const handleLogin = () => {
  loginFormRef.value.validate((error: boolean) => {
    if (!error) {
      authenticationApi.login(loginForm.value).then((res: ResponseData) => {
        if (res?.data) {
          localStorage.setLoginUser(res.data)
          // 设置为true，确保默认显示文字
          localStorage.set(HOME_CARD, true)
          router.push('/')
        }
      })
    }
  })
}
// 注册操作
const handleRegister = () => {
  registerFormRef.value.validate((error: boolean) => {
    if (!error) {
      authenticationApi.register(registerForm.value).then((res: ResponseData) => {
        if (res.data) {
          localStorage.setLoginUser(res.data)
          // 设置为true，确保默认显示文字
          localStorage.set(HOME_CARD, true)
          router.push('/')
        }
      })
    }
  })
}
const loginInputNum = (e: any) => {
  return (loginForm.value.phone = e.replace(/\D/g, ''))
}
const registerInputNum = (e: any) => {
  return (registerForm.value.phone = e.replace(/\D/g, ''))
}
// 动态计算cardStyle 样式
const cardStyle = computed(() => {
  return {
    paddingTop: '2rem',
    width: '22.5rem',
    transition: 'transform 0.6s, box-shadow 0.3s',
    transformStyle: 'preserve-3d',
    transform: isFlipped.value ? 'rotateY(180deg)' : 'none',
    backgroundColor: 'var(--creamy-white-1)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
    backdropFilter: 'blur(5px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    opacity: 0.8,
  }
})
</script>
<style scoped>
.layout-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  height: 100dvh;
  position: relative;
  z-index: 1;
}

.card-container {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.login-form {
  padding: 0.6rem;
}

.register-form {
  padding: 0.6rem;
  transform: rotateY(180deg);
}

.login-form-ipt,
.register-form-ipt {
  width: 11rem;
  background-color: var(--creamy-white-1);
}

.login-form-btn,
.register-form-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem 1.25rem;
}

.login-btn,
.register-btn,
.flip-btn {
  width: 6.25rem;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22.5rem;
  height: 200px;
  min-height: 200px;
  margin-bottom: 1rem;
  z-index: 2;
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 10%));
}

.footer-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1.5rem;
  z-index: 2;
}
</style>
