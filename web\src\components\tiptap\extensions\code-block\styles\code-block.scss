@use './syntax-highlight' as *;

/* 代码块样式 - Typora风格 */
.ProseMirror {
  pre {
    position: relative;
    background-color: var(--creamy-white-1, #eeece4);
    border-radius: 4px;
    padding: 0;
    margin: 1rem 0;
    overflow: hidden; /* 防止内容超出容器 */
    font-family: Consolas, 'Source Code Pro', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    border: 1px solid var(--creamy-white-3, #dcd8ca);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 5%);
    max-width: 100%; /* 确保不超出父容器 */
    width: 100%; /* 占满可用宽度 */
    box-sizing: border-box; /* 包含边框和内边距 */

    /* 禁用代码块的选择功能 */
    user-select: none;

    /* 禁用拖拽功能 */
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;

    /* 禁用右键菜单 */
    pointer-events: none;
    
    /* 确保代码块在编辑模式下可以获得焦点 */
    &.editable-mode {
      pointer-events: auto;
    }

    /* 代码块头部样式 */
    .code-block-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--creamy-white-2, #e4e1d8);
      padding: 0.4rem 0.8rem;
      border-bottom: 1px solid var(--creamy-white-3, #dcd8ca);
      font-family:
        -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
        'Open Sans', 'Helvetica Neue', sans-serif;
      font-size: 0.85rem;
      color: var(--gray-5, rgba(28, 25, 23, 60%));
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      flex-shrink: 0; /* 防止头部被压缩 */

      /* 重新启用头部的pointer-events，允许工具按钮交互 */
      pointer-events: auto;

      /* 语言标识样式 */
      .code-block-language {
        font-weight: 500;
        text-transform: lowercase;
        flex-shrink: 0; /* 防止语言标识被压缩 */ /* 始终不可选择 */
        user-select: none !important;
        pointer-events: none !important; /* 始终不可交互 */
        cursor: default !important;
      }

      /* 工具栏样式 */
      .code-block-toolbar {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0; /* 防止工具栏被压缩 */
      }
    }

    /* 代码内容容器 */
    code {
      display: block;
      color: var(--code-text, var(--black, #2e2b29));
      padding: 0.8rem 1rem;
      background: none;
      font-family: inherit;
      white-space: pre;
      tab-size: 4;
      overflow: visible; /* 让 NScrollbar 处理滚动 */
      max-width: 100%; /* 确保不超出容器 */
      box-sizing: border-box; /* 包含padding在内的盒模型 */
    }

    /* 滚动条容器 - 统一样式 */
    .code-scrollbar-container {
      width: 100%;
      max-width: 100%; /* 确保不超出容器 */
      overflow: hidden; /* 防止内容溢出 */
      transition: none; /* 防止换行切换时的抖动 */
      box-sizing: border-box; /* 包含边框和内边距 */
      position: relative; /* 为滚动条定位提供参考 */

      /* NScrollbar统一样式（编辑模式和只读模式） */
      .n-scrollbar {
        width: 100%;
        max-width: 100%; /* 确保不超出容器 */
        transition: none; /* 防止换行切换时的抖动 */

        .n-scrollbar-content {
          /* 让 NScrollbar 自己处理内容溢出 */
        }

        /* 水平滚动条样式 */
        .n-scrollbar-rail--horizontal {
          height: 8px;
          background-color: transparent;
          border-radius: 4px;
          bottom: 2px; /* 稍微向上偏移 */
          display: block !important; /* 强制显示 */
          opacity: 1 !important; /* 确保可见 */

          .n-scrollbar-thumb {
            background-color: var(--gray-4, rgba(53, 38, 28, 30%));
            border-radius: 4px;
            transition: background-color 0.2s ease;
            min-width: 20px; /* 确保滚动条有最小宽度 */

            &:hover {
              background-color: var(--gray-5, rgba(28, 25, 23, 50%));
            }
          }
        }

        /* 确保在非换行模式下水平滚动条可见 */
        &:not(.code-wrap) .n-scrollbar-rail--horizontal {
          display: block !important;
          opacity: 1 !important;
        }

        /* 换行模式下隐藏水平滚动条 */
        &.code-wrap .n-scrollbar-rail--horizontal {
          display: none !important;
        }

        /* 垂直滚动条样式（保持一致性） */
        .n-scrollbar-rail--vertical {
          width: 8px;
          background-color: transparent;
          border-radius: 4px;
          right: 2px; /* 稍微向左偏移 */

          .n-scrollbar-thumb {
            background-color: var(--gray-4, rgba(53, 38, 28, 30%));
            border-radius: 4px;
            transition: background-color 0.2s ease;
            min-height: 20px; /* 确保滚动条有最小高度 */

            &:hover {
              background-color: var(--gray-5, rgba(28, 25, 23, 50%));
            }
          }
        }
      }

      /* 编辑模式特殊样式 */
      &.editable-mode {
        code[contenteditable='true'] {
          /* 编辑模式下的代码元素样式 */
          outline: none;
          border: none;
          background: transparent;
          cursor: text; /* 显示文本光标 */

          /* 保持编辑时的光标样式 */
          &:focus {
            outline: none;
            box-shadow: none;
          }

          /* 选中文本的样式 */
          &::selection {
            background-color: rgba(75, 163, 253, 20%);
            color: inherit;
          }
        }
      }

      /* 只读模式特殊样式 */
      &.readonly-mode {
        code {
          cursor: default; /* 只读模式下显示默认光标 */
          user-select: text; /* 允许选择文本 */

          /* 可选择代码内容的特殊样式 */
          &.code-selectable {
            user-select: text !important;
            pointer-events: auto !important; /* 允许交互以便选择 */
            cursor: text; /* 显示文本选择光标 */

            /* 选中文本的样式 */
            &::selection {
              background-color: rgba(75, 163, 253, 25%);
              color: inherit;
            }
          }
        }
      }
    }

    /* 换行模式 */
    &.code-wrap code,
    code.code-wrap-enabled {
      white-space: pre-wrap;
      word-break: break-all;
      overflow: visible; /* 让 NScrollbar 处理 */
    }

    /* 非换行模式下的滚动 */
    &:not(.code-wrap) code {
      overflow: visible; /* 让 NScrollbar 处理滚动 */
    }

    /* 工具按钮通用样式 */
    .code-copy-button,
    .code-wrap-button {
      width: 24px;
      height: 24px;
      padding: 4px;
      background-color: transparent;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      svg {
        width: 16px;
        height: 16px;
        stroke: var(--gray-5, rgba(28, 25, 23, 60%));
        transition: all 0.2s ease;
      }

      &:hover {
        background-color: var(--creamy-white-3, #dcd8ca);

        svg {
          stroke: var(--black, #2e2b29);
        }
      }

      /* 按钮激活状态 */
      &.active {
        background-color: var(--creamy-white-3, #dcd8ca);
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 10%);

        svg {
          stroke: var(--black, #2e2b29);
        }
      }
    }

    /* 复制按钮成功状态 */
    .code-copy-button.copied {
      background-color: rgba(34, 197, 94, 10%);
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 5%);

      svg {
        stroke: var(--green, #22c55e);
      }
    }

    /* 换行按钮激活状态 */
    .code-wrap-button.active {
      background-color: rgba(75, 163, 253, 10%);
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 5%);

      svg {
        stroke: var(--blue, #4ba3fd);
      }
    }
  }

  /* 只读模式下的代码块样式 */
  &.ProseMirror-readonly pre {
    background-color: var(--creamy-white-1, #eeece4);
    border-color: var(--creamy-white-3, #dcd8ca);
  }

  /* 不可选择的代码块样式 */
  pre.code-block-readonly {
    /* 强化不可选择样式 */
    user-select: none !important;

    /* 禁用所有交互，除了工具按钮 */
    pointer-events: none !important;

    /* 工具按钮区域重新启用交互 */
    .code-block-header {
      pointer-events: auto !important;
    }

    /* 代码内容在只读模式下可选择 */
    &.readonly-mode code.code-selectable {
      pointer-events: auto !important;
      user-select: text !important;
      cursor: text !important;
    }

    /* 编辑模式下代码内容不可交互 */
    &.editable-mode code {
      pointer-events: none !important;
      user-select: none !important;
      cursor: default !important;
    }
  }
}
