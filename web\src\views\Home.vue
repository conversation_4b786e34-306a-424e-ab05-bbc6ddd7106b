<template>
  <div class="common-layout">
    <!-- 顶部区域 -->
    <div class="common-layout-top">
      <transition name="fade-slide">
        <div class="left-controls-container" v-show="!isCardVisible">
          <div class="control-item">
            <NGradientText type="info" class="control-label">循环：</NGradientText>
            <NSwitch v-model:value="danmakuLoop" size="small" />
          </div>
          <div class="control-item">
            <NGradientText type="info" class="control-label">暂停：</NGradientText>
            <NSwitch
              v-model:value="danmakuPause"
              @update:value="handleDanmakuPauseChange"
              size="small"
            />
          </div>
        </div>
      </transition>
      <div class="middle-controls-container">
        <!-- 文章按钮容器，包含用于切换显示状态的按钮 -->
        <ToggleButton v-model:value="isCardVisible" @toggle="toggleCardVisibility" />

        <!-- 搜索容器 -->
        <SearchBar v-model="searchCondition" :placeholder="searchPlaceholder" @search="search" />

        <!-- 创建按钮容器 -->
        <CreateButton @click="openCreateArticleDialog" />
      </div>

      <!-- 创建文章弹框 -->
      <ArticleModal
        ref="articleModalRef"
        @success="
          () => {
            resetArticleList()
            search()
          }
        "
      />
      <UserInfoGroup />
    </div>

    <!-- 标签栏区域 -->
    <div class="tag-bar-wrapper">
      <TagBar v-model="searchCondition.tag" @tagSelected="handleTagSelected" />
    </div>

    <div class="common-layout-content">
      <!-- 过渡卡片区域，极简过渡以提高速度 -->
      <transition
        @before-enter="transition.beforeEnter"
        @enter="transition.enter"
        @leave="transition.leave"
        :duration="{ enter: 200, leave: 200 }"
        mode="out-in"
      >
        <ArticleList
          v-if="isCardVisible"
          :key="'article'"
          :search-condition="searchCondition"
          ref="articleListRef"
          @reset="resetArticleList"
        />

        <!-- 过渡评论区域，极简过渡以提高速度 -->
        <CommentDanmaku
          v-else
          :key="'comment'"
          :search-condition="searchCondition"
          :loop="danmakuLoop"
          :pause="danmakuPause"
          ref="commentDanmakuRef"
        />
      </transition>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { NSwitch, NGradientText } from 'naive-ui'
import { ref, onMounted, computed, onUnmounted, defineAsyncComponent } from 'vue'
import { useMessage } from 'naive-ui'

import commentApi from '@/api/comment'
import userApi from '@/api/user'

// 导入新抽取的组件
import ArticleList from '@/components/home/<USER>'
import CommentDanmaku from '@/components/home/<USER>'
import CreateButton from '@/components/home/<USER>'
import SearchBar from '@/components/home/<USER>'
import TagBar from '@/components/home/<USER>'
import ToggleButton from '@/components/home/<USER>'
import UserInfoGroup from '@/components/UserInfoGroup.vue'
import { TOPIC_COMMENTS } from '@/constants/destination.constants'
import { HOME_ARTICLE_SEARCH, HOME_COMMENT_SEARCH } from '@/constants/frequency_key.constants'
import { HOME_CARD, HOME_SEARCH_CONDITION } from '@/constants/storage.constants'
import type { ResponseData } from '@/types/response_data.types'
import type { SearchCondition } from '@/types/search.types'
import frequencyLimit from '@/utils/frequency-limit'
import localStorage from '@/utils/local-storage'
import transition from '@/utils/transition'
import webSocket from '@/utils/web-socket'

// 导入异步组件
const ArticleModal = defineAsyncComponent(() => import('@/components/ArticleModal.vue'))

// 添加消息提示
const message = useMessage()

// 添加加载状态控制
const isLoading = ref(false)
// 添加请求取消控制器
let currentRequestController: AbortController | null = null

// 初始化在线人数
const onlineCount = ref<number>(0)

// 搜索占位符
const searchPlaceholder = computed(() => {
  return isCardVisible.value ? '感兴趣的文章' : '有意思的评论'
})

// 条件筛选
const searchCondition = ref<SearchCondition>({
  searchKey: '',
  owner: false,
  interaction: false,
  favorite: false,
  tag: '',
})

// 弹幕控制状态
const danmakuLoop = ref(false)
const danmakuPause = ref(false)

// 处理弹幕暂停状态变化
const handleDanmakuPauseChange = (newVal: boolean) => {
  if (commentDanmakuRef.value) {
    if (newVal) {
      commentDanmakuRef.value.pause()
    } else {
      commentDanmakuRef.value.play()
    }
  }
}

// 文章弹框引用
const articleModalRef = ref()
const articleListRef = ref()
const commentDanmakuRef = ref()

// 显示状态控制
const isCardVisible = ref(true)

// 添加统一的搜索防抖key
const UNIFIED_SEARCH_KEY = 'unified_search'
// 添加搜索状态锁
const isSearching = ref(false)

// 生命周期钩子
onMounted(() => {
  webSocket.connect()

  // 加载保存的搜索条件
  loadSearchCondition()

  // 从localStorage读取展示类型，如果有值则使用该值，保持用户的选择
  const savedCardVisibility = localStorage.get(HOME_CARD)
  if (savedCardVisibility !== null && savedCardVisibility !== undefined) {
    // 确保转换为布尔值
    isCardVisible.value = Boolean(savedCardVisibility)
  }

  // 默认进行无条件搜索
  search()

  // 窗口大小变化监听
  window.addEventListener('resize', resizeCallback)
})

onUnmounted(() => {
  if (currentRequestController) {
    currentRequestController.abort()
    currentRequestController = null
  }
  window.removeEventListener('resize', resizeCallback)
})

// 窗口大小调整回调
const resizeCallback = () => {
  if (!isCardVisible.value && commentDanmakuRef.value) {
    commentDanmakuRef.value.resize()
  }
}

// 搜索函数
const search = (loadMore: boolean = false) => {
  // 如果正在搜索中，直接返回
  if (isSearching.value) return

  // 如果正在加载中，取消之前的请求
  if (currentRequestController) {
    currentRequestController.abort()
    currentRequestController = null
  }

  // 创建新的请求控制器
  currentRequestController = new AbortController()

  // 使用统一的防抖key
  frequencyLimit.debounce(
    UNIFIED_SEARCH_KEY,
    () => {
      if (isCardVisible.value) {
        // 文章搜索
        if (loadMore) return // 由ArticleList组件内部处理加载更多

        if (articleListRef.value) {
          isSearching.value = true
          isLoading.value = true
          articleListRef.value
            .loadArticles(false, currentRequestController?.signal)
            .catch((error: any) => {
              // 只有在非取消请求的情况下才显示错误
              if (!(error.name === 'CanceledError' || error.message === 'canceled')) {
                message.error('加载文章失败，请稍后重试')
              }
            })
            .finally(() => {
              isLoading.value = false
              isSearching.value = false
              currentRequestController = null
            })
        }
      } else {
        // 评论搜索
        // 检查是否有任何搜索条件，包括标签
        const hasSearchCondition = Object.entries(searchCondition.value).some(([key, value]) => {
          if (key === 'tag') return !!value // 检查tag是否有值
          if (typeof value === 'boolean') return value // 检查布尔值条件
          if (key === 'searchKey') return !!value // 检查searchKey是否有值
          return false
        })

        if (!hasSearchCondition) {
          if (commentDanmakuRef.value) {
            danmakuLoop.value = false
          }
          return
        }

        isSearching.value = true
        isLoading.value = true
        commentApi
          .search(searchCondition.value, currentRequestController?.signal)
          .then((res: ResponseData) => {
            if (commentDanmakuRef.value) {
              commentDanmakuRef.value.addCommentList(res.data)
            }
          })
          .catch((error: any) => {
            // 只有在非取消请求的情况下才显示错误
            if (!(error.name === 'CanceledError' || error.message === 'canceled')) {
              message.error('加载评论失败，请稍后重试')
            }
          })
          .finally(() => {
            isLoading.value = false
            isSearching.value = false
            currentRequestController = null
          })
      }
    },
    1000,
  )
}

// 重置文章列表
const resetArticleList = () => {
  if (articleListRef.value) {
    articleListRef.value.resetList()
  }
}

// 打开创建文章弹框
const openCreateArticleDialog = () => {
  articleModalRef.value.openCreateArticleDialog()
}

// 切换卡片显示状态
const toggleCardVisibility = () => {
  // 记录之前的状态
  const wasCardVisible = isCardVisible.value

  // 确保存储的是布尔值
  localStorage.set(HOME_CARD, Boolean(isCardVisible.value))

  // 处理状态切换
  if (isCardVisible.value) {
    // 从评论视图切换到文章视图
    if (commentDanmakuRef.value) {
      commentDanmakuRef.value.unsubscribeComment()
      commentDanmakuRef.value.clearDanmaku()
    }

    // 始终重置文章列表并重新加载数据，确保数据最新
    resetArticleList()
    // 添加小延迟，确保状态完全切换
    setTimeout(() => {
      search()
    }, 100)
  } else {
    // 从文章视图切换到评论视图
    if (commentDanmakuRef.value) {
      commentDanmakuRef.value.subscribeComment()
    }
    // 添加小延迟，确保状态完全切换
    setTimeout(() => {
      search()
    }, 100)
  }
}

// 从localStorage加载搜索条件
const loadSearchCondition = () => {
  const savedCondition = localStorage.get(HOME_SEARCH_CONDITION)
  if (savedCondition) {
    // 类型断言确保类型正确
    searchCondition.value = savedCondition as SearchCondition
  }
}

// 处理标签选择
const handleTagSelected = (tagName: string) => {
  // 标签已经通过v-model更新到searchCondition.tag
  // 保存搜索条件到localStorage
  localStorage.set(HOME_SEARCH_CONDITION, searchCondition.value)
  // 触发搜索
  search()
}
</script>

<style scoped lang="scss">
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  height: 100dvh;
  width: 100vw;
  width: 100dvw;

  .common-layout-top {
    padding: 1.25rem;
    background: linear-gradient(to bottom, var(--creamy-white-3), var(--creamy-white-2));
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 7.5rem;
    flex-wrap: wrap;

    .left-controls-container {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      position: absolute;
      left: 1.25rem;
      top: 7rem;

      .control-item {
        display: flex;
        align-items: center;
        color: var(--black);
        font-size: 0.9rem;

        .control-label {
          margin-right: 0;
        }
      }
    }

    .middle-controls-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: auto;
      padding-left: 4.5%;
      max-width: 100%;
    }
  }

  .tag-bar-wrapper {
    min-height: 2.5rem;
    padding-bottom: 0.25rem;
    background-color: var(--creamy-white-2);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .common-layout-content {
    height: calc(100vh - 12.5rem);
    height: calc(100dvh - 12.5rem);
    background-color: var(--creamy-white-1);
    position: relative;
    overflow: hidden;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 0.25rem;
  }
}

/* 弹幕控制按钮的过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
