package com.shenmo.wen.app.authentication.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.google.common.base.Supplier;
import com.shenmo.wen.app.authentication.config.properties.UserConfigProperties;
import com.shenmo.wen.app.authentication.exception.AuthenticationException;
import com.shenmo.wen.app.authentication.exception.AuthenticationExceptionEnum;
import com.shenmo.wen.app.authentication.pojo.param.LoginParam;
import com.shenmo.wen.app.authentication.pojo.param.RegisterParam;
import com.shenmo.wen.app.authentication.service.WenAuthenticationService;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.IpUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;
import com.shenmo.wen.modules.user.pojo.vo.WenUserVo;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
public class WenAuthenticationServiceImpl implements WenAuthenticationService {
    private final WenUserMapper mapper;
    private final PasswordEncoder passwordEncoder;
    private final UserConfigProperties userConfigProperties;

    @Override
    public WenUserVo login(LoginParam param) {
        final String phone = param.getPhone();
        final String password = param.getPassword();
        final Supplier<AuthenticationException> phoneOrPasswordMistakeExceptionSupplier = () -> new AuthenticationException(AuthenticationExceptionEnum.PHONE_OR_PASSWORD_MISTAKE);
        final WenUser user = Optional.ofNullable(mapper.byPhone(phone)).orElseThrow(phoneOrPasswordMistakeExceptionSupplier);
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw phoneOrPasswordMistakeExceptionSupplier.get();
        }
        final String ip = IpUtils.getIp();
        final Long id = user.getId();
        user.setIp(ip);
        mapper.updateIpById(id, ip);
        final WenUserVo userVo = new WenUserVo();
        BeanUtils.copyProperties(user, userVo);
        userVo.setIpLocation(IpUtils.getIpLocation(ip));
        StpUtil.login(id);
        return userVo;
    }

    @Override
    public WenUserVo register(RegisterParam param) {
        final String phone = param.getPhone();
        AssertUtils.isTrue(mapper.countByPhone(phone) == 0, AuthenticationExceptionEnum.PHONE_EXISTS);
        final String username = param.getUsername();
        AssertUtils.isTrue(mapper.countByUsername(username) == 0, AuthenticationExceptionEnum.USERNAME_EXISTS);
        final WenUser user = new WenUser();
        user.setUsername(username);
        user.setPhone(phone);
        final String password = param.getPassword();
        user.setPassword(passwordEncoder.encode(password));
        user.setAvatar(userConfigProperties.getAvatar());
        final String ip = IpUtils.getIp();
        user.setIp(ip);
        mapper.insert(user);
        final WenUserVo userVo = new WenUserVo();
        BeanUtils.copyProperties(user, userVo);
        userVo.setIpLocation(IpUtils.getIpLocation(ip));
        StpUtil.login(user.getId());
        return userVo;
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }
}
