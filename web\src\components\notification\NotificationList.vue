<template>
  <div class="notification-list-container">
    <n-data-table
      class="notification-table"
      :remote="true"
      :loading="loading"
      :data="notifications"
      :row-props="rowProps"
      :columns="notificationColumns"
      :bordered="false"
    />
    <NPagination
      class="notification-pagination"
      :page="pagination.page"
      :page-size="pagination.pageSize"
      :show-size-picker="pagination.showSizePicker"
      :show-quick-jumper="pagination.showQuickJumper"
      :page-slot="pagination.pageSlot"
      :page-sizes="pagination.pageSizes"
      :size="pagination.size"
      :show-quick-jump-dropdown="pagination.showQuickJumpDropdown"
      :prefix="pagination.prefix"
      :suffix="pagination.suffix"
      :itemCount="pagination.itemCount"
      @update:page="pagination.onUpdatePage"
      @update:page-size="pagination.onUpdatePageSize"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, computed, watch, onMounted } from 'vue'
import { NDataTable, NPagination, NPopover, NBadge, NButton, NIcon } from 'naive-ui'
import type { PaginationProps } from 'naive-ui'
import { ArrowRight20Filled } from '@vicons/fluent'
import { type Notification } from '@/types/notification.types'
import notificationApi from '@/api/notification'
import type { ResponseData } from '@/types/response_data.types'
import dateTime from '@/utils/date-time'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import tiptap from '@/utils/tiptap'
import Json from '@/utils/json'
import { COMMENT } from '@/constants/bucket.constants'
import { COMMENT_EXTENSIONS } from '@/constants/tiptap.constants'
import { activeTheme, ThemeType } from '@/utils/theme'
import logger from '@/utils/log'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['notification-click'])

// 添加主题监听
const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

const loading = ref(false)
const notifications = ref()
const commentFlag = '评论了：'

// 分页配置
const pageSizes = [
  { label: '', value: 5 },
  { label: '', value: 10 },
  { label: '', value: 15 },
]
pageSizes.forEach((item) => {
  item.label = `${item.value}/页`
})

const pagination = ref<PaginationProps>({
  page: 1,
  pageSize: 5,
  showSizePicker: true,
  showQuickJumper: false,
  pageSlot: 5,
  pageSizes,
  size: 'medium',
  showQuickJumpDropdown: false,
  prefix: (info: {
    startIndex: number
    endIndex: number
    page: number
    pageSize: number
    pageCount: number
    itemCount: number | undefined
  }) => {
    return <span>{`第 ${info.page} 页 `}</span>
  },
  suffix: (info: {
    startIndex: number
    endIndex: number
    page: number
    pageSize: number
    pageCount: number
    itemCount: number | undefined
  }) => {
    return <span>{`共 ${info.itemCount} 条`}</span>
  },
  onUpdatePage: (page: number) => {
    pagination.value.page = page
    loadNotificationPage()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
    loadNotificationPage()
  },
})

// 表格列定义
const notificationColumns = [
  {
    title: '通知时间',
    key: 'ctTm',
    width: 162,
  },
  {
    title: '通知内容',
    key: 'content',
    ellipsis: true,
    render(row: Notification) {
      const commentId = row.commentId
      const content = row.content
      const index = content.indexOf(commentFlag)
      return (
        <span>
          <NBadge style="position: absolute;" dot={!row.isRead} offset={[-4, 0]} />
          <NPopover
            trigger="click"
            placement="top-start"
            style={'max-width:min(555px,84vw); margin-left:min(-180px,40vw)'}
            flip={false}
            v-slots={{
              trigger: () => (
                <span
                  class="cursor-pointer notification-content"
                  onClick={(e) => e.stopPropagation()}
                >
                  {commentId
                    ? content.substring(0, index + commentFlag.length) +
                      tiptap.serializeContent(
                        safeJsonParse(content.substring(index + commentFlag.length)),
                      )
                    : row.content}
                </span>
              ),
              default: () => (
                <div class="notification-popover-content">
                  {commentId ? (
                    <div style="margin: 10px">
                      {content.substring(0, index + commentFlag.length)}
                      <TiptapEditor
                        fileBucket={COMMENT}
                        modelValue={safeJsonParse(content.substring(index + commentFlag.length))}
                        extensions={COMMENT_EXTENSIONS}
                        editable={false}
                      />
                    </div>
                  ) : (
                    <div>{row.content}</div>
                  )}
                  <NButton
                    style={'margin-left:auto'}
                    class={'flex-column-end'}
                    text={true}
                    type="primary"
                    onClick={() => {
                      handleNotificationClick(row)
                    }}
                  >
                    让我看看
                    <NIcon size="16">
                      <ArrowRight20Filled />
                    </NIcon>
                  </NButton>
                </div>
              ),
            }}
          />
        </span>
      )
    },
  },
]

// 行属性
const rowProps = (row: any) => {
  return {}
}

// 处理通知点击
const handleNotificationClick = (notification: Notification) => {
  emit('notification-click', notification)
}

// 加载通知数据
const loadNotificationPage = () => {
  loading.value = true

  // 加载通知列表
  notificationApi
    .load({
      pageNo: pagination.value.page,
      pageSize: pagination.value.pageSize,
    })
    .then((res: ResponseData) => {
      const data = res?.data
      if (data) {
        // 处理时间格式
        data.rows.forEach((e: any) => {
          e.ctTm = dateTime.toTimeString(e.ctTm)
        })
        pagination.value.itemCount = data.totalRows
        pagination.value.pageCount = data.totalPage
        notifications.value = data.rows
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 监听可见性变化，当显示时加载通知 - 确保在loadNotificationPage定义之后
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      loadNotificationPage()
    }
  },
  { immediate: true }, // 立即触发一次监听器
)

// 组件挂载时，如果可见则加载数据
onMounted(() => {
  if (props.visible) {
    loadNotificationPage()
  }
})

// 导出加载方法，供外部调用
defineExpose({
  loadNotificationPage,
  resetPagination: () => {
    // 重置分页到第一页
    pagination.value.page = 1
    pagination.value.pageSize = 5
  },
})

const safeJsonParse = (content: string) => {
  if (!content) {
    return { type: 'doc', content: [{ type: 'paragraph', content: [] }] }
  }

  try {
    // 正常情况下使用tiptap处理
    return tiptap.toJsonObject(content)
  } catch (error) {
    // 如果解析失败，创建一个包含原始内容的文本结构
    logger.error('通知内容JSON解析失败:', error)
    return {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: typeof content === 'string' ? content : '内容无法显示',
            },
          ],
        },
      ],
    }
  }
}
</script>

<style scoped>
.notification-table :deep(.n-pagination) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: nowrap;
}

.notification-table :deep(.n-pagination-prefix) {
  white-space: nowrap;
  margin-right: 0.5rem;
}

.notification-pagination {
  display: flex;
  flex-wrap: wrap;
  margin-top: 1rem;
  justify-content: center;
  row-gap: 0.5rem;
}

.notification-popover-content {
  max-width: 500px;
}

.cursor-pointer {
  cursor: pointer;
}

.flex-column-end {
  display: flex;
  align-items: center;
}
</style>
