var VueDraggablePlus=function(x,g){"use strict";var Un=Object.defineProperty;var qe=Object.getOwnPropertySymbols;var Zt=Object.prototype.hasOwnProperty,Qt=Object.prototype.propertyIsEnumerable;var Jt=(x,g,B)=>g in x?Un(x,g,{enumerable:!0,configurable:!0,writable:!0,value:B}):x[g]=B,ve=(x,g)=>{for(var B in g||(g={}))Zt.call(g,B)&&Jt(x,B,g[B]);if(qe)for(var B of qe(g))Qt.call(g,B)&&Jt(x,B,g[B]);return x};var mt=(x,g)=>{var B={};for(var Q in x)Zt.call(x,Q)&&g.indexOf(Q)<0&&(B[Q]=x[Q]);if(x!=null&&qe)for(var Q of qe(x))g.indexOf(Q)<0&&Qt.call(x,Q)&&(B[Q]=x[Q]);return B};const B="[vue-draggable-plus]: ";function Q(t){console.warn(B+t)}function en(t){console.error(B+t)}function vt(t,e,n){return n>=0&&n<t.length&&t.splice(n,0,t.splice(e,1)[0]),t}function tn(t){return t.replace(/-(\w)/g,(e,n)=>n?n.toUpperCase():"")}function nn(t){return Object.keys(t).reduce((e,n)=>(typeof t[n]!="undefined"&&(e[tn(n)]=t[n]),e),{})}function bt(t,e){return Array.isArray(t)&&t.splice(e,1),t}function yt(t,e,n){return Array.isArray(t)&&t.splice(e,0,n),t}function on(t){return typeof t=="undefined"}function rn(t){return typeof t=="string"}function wt(t,e,n){const o=t.children[n];t.insertBefore(e,o)}function Ke(t){t.parentNode&&t.parentNode.removeChild(t)}function an(t,e=document){var o;let n=null;return typeof(e==null?void 0:e.querySelector)=="function"?n=(o=e==null?void 0:e.querySelector)==null?void 0:o.call(e,t):n=document.querySelector(t),n||Q(`Element not found: ${t}`),n}function ln(t,e,n=null){return function(...o){return t.apply(n,o),e.apply(n,o)}}function sn(t,e){const n=ve({},t);return Object.keys(e).forEach(o=>{n[o]?n[o]=ln(t[o],e[o]):n[o]=e[o]}),n}function un(t){return t instanceof HTMLElement}function Et(t,e){Object.keys(t).forEach(n=>{e(n,t[n])})}function fn(t){return t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&(t.charCodeAt(2)>122||t.charCodeAt(2)<97)}const cn=Object.assign;/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function St(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,o)}return n}function ne(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?St(Object(n),!0).forEach(function(o){dn(t,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):St(Object(n)).forEach(function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(n,o))})}return t}function Fe(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Fe=function(e){return typeof e}:Fe=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fe(t)}function dn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ie(){return ie=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},ie.apply(this,arguments)}function hn(t,e){if(t==null)return{};var n={},o=Object.keys(t),r,i;for(i=0;i<o.length;i++)r=o[i],!(e.indexOf(r)>=0)&&(n[r]=t[r]);return n}function pn(t,e){if(t==null)return{};var n=hn(t,e),o,r;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)o=i[r],!(e.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(t,o)&&(n[o]=t[o])}return n}var gn="1.15.2";function ae(t){if(typeof window!="undefined"&&window.navigator)return!!navigator.userAgent.match(t)}var le=ae(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),_e=ae(/Edge/i),Dt=ae(/firefox/i),Te=ae(/safari/i)&&!ae(/chrome/i)&&!ae(/android/i),_t=ae(/iP(ad|od|hone)/i),Tt=ae(/chrome/i)&&ae(/android/i),Ct={capture:!1,passive:!1};function _(t,e,n){t.addEventListener(e,n,!le&&Ct)}function S(t,e,n){t.removeEventListener(e,n,!le&&Ct)}function Re(t,e){if(e){if(e[0]===">"&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(n){return!1}return!1}}function mn(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function ee(t,e,n,o){if(t){n=n||document;do{if(e!=null&&(e[0]===">"?t.parentNode===n&&Re(t,e):Re(t,e))||o&&t===n)return t;if(t===n)break}while(t=mn(t))}return null}var Ot=/\s+/g;function U(t,e,n){if(t&&e)if(t.classList)t.classList[n?"add":"remove"](e);else{var o=(" "+t.className+" ").replace(Ot," ").replace(" "+e+" "," ");t.className=(o+(n?" "+e:"")).replace(Ot," ")}}function h(t,e,n){var o=t&&t.style;if(o){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),e===void 0?n:n[e];!(e in o)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),o[e]=n+(typeof n=="string"?"":"px")}}function be(t,e){var n="";if(typeof t=="string")n=t;else do{var o=h(t,"transform");o&&o!=="none"&&(n=o+" "+n)}while(!e&&(t=t.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(n)}function It(t,e,n){if(t){var o=t.getElementsByTagName(e),r=0,i=o.length;if(n)for(;r<i;r++)n(o[r],r);return o}return[]}function oe(){var t=document.scrollingElement;return t||document.documentElement}function N(t,e,n,o,r){if(!(!t.getBoundingClientRect&&t!==window)){var i,a,l,s,u,d,c;if(t!==window&&t.parentNode&&t!==oe()?(i=t.getBoundingClientRect(),a=i.top,l=i.left,s=i.bottom,u=i.right,d=i.height,c=i.width):(a=0,l=0,s=window.innerHeight,u=window.innerWidth,d=window.innerHeight,c=window.innerWidth),(e||n)&&t!==window&&(r=r||t.parentNode,!le))do if(r&&r.getBoundingClientRect&&(h(r,"transform")!=="none"||n&&h(r,"position")!=="static")){var v=r.getBoundingClientRect();a-=v.top+parseInt(h(r,"border-top-width")),l-=v.left+parseInt(h(r,"border-left-width")),s=a+i.height,u=l+i.width;break}while(r=r.parentNode);if(o&&t!==window){var w=be(r||t),y=w&&w.a,D=w&&w.d;w&&(a/=D,l/=y,c/=y,d/=D,s=a+d,u=l+c)}return{top:a,left:l,bottom:s,right:u,width:c,height:d}}}function At(t,e,n){for(var o=ue(t,!0),r=N(t)[e];o;){var i=N(o)[n],a=void 0;if(a=r>=i,!a)return o;if(o===oe())break;o=ue(o,!1)}return!1}function ye(t,e,n,o){for(var r=0,i=0,a=t.children;i<a.length;){if(a[i].style.display!=="none"&&a[i]!==p.ghost&&(o||a[i]!==p.dragged)&&ee(a[i],n.draggable,t,!1)){if(r===e)return a[i];r++}i++}return null}function Je(t,e){for(var n=t.lastElementChild;n&&(n===p.ghost||h(n,"display")==="none"||e&&!Re(n,e));)n=n.previousElementSibling;return n||null}function Z(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)t.nodeName.toUpperCase()!=="TEMPLATE"&&t!==p.clone&&(!e||Re(t,e))&&n++;return n}function xt(t){var e=0,n=0,o=oe();if(t)do{var r=be(t),i=r.a,a=r.d;e+=t.scrollLeft*i,n+=t.scrollTop*a}while(t!==o&&(t=t.parentNode));return[e,n]}function vn(t,e){for(var n in t)if(t.hasOwnProperty(n)){for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t[n][o])return Number(n)}return-1}function ue(t,e){if(!t||!t.getBoundingClientRect)return oe();var n=t,o=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var r=h(n);if(n.clientWidth<n.scrollWidth&&(r.overflowX=="auto"||r.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(r.overflowY=="auto"||r.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return oe();if(o||e)return n;o=!0}}while(n=n.parentNode);return oe()}function bn(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function Ze(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}var Ce;function Pt(t,e){return function(){if(!Ce){var n=arguments,o=this;n.length===1?t.call(o,n[0]):t.apply(o,n),Ce=setTimeout(function(){Ce=void 0},e)}}}function yn(){clearTimeout(Ce),Ce=void 0}function Nt(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function Mt(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}function Ft(t,e,n){var o={};return Array.from(t.children).forEach(function(r){var i,a,l,s;if(!(!ee(r,e.draggable,t,!1)||r.animated||r===n)){var u=N(r);o.left=Math.min((i=o.left)!==null&&i!==void 0?i:1/0,u.left),o.top=Math.min((a=o.top)!==null&&a!==void 0?a:1/0,u.top),o.right=Math.max((l=o.right)!==null&&l!==void 0?l:-1/0,u.right),o.bottom=Math.max((s=o.bottom)!==null&&s!==void 0?s:-1/0,u.bottom)}}),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var $="Sortable"+new Date().getTime();function wn(){var t=[],e;return{captureAnimationState:function(){if(t=[],!!this.options.animation){var o=[].slice.call(this.el.children);o.forEach(function(r){if(!(h(r,"display")==="none"||r===p.ghost)){t.push({target:r,rect:N(r)});var i=ne({},t[t.length-1].rect);if(r.thisAnimationDuration){var a=be(r,!0);a&&(i.top-=a.f,i.left-=a.e)}r.fromRect=i}})}},addAnimationState:function(o){t.push(o)},removeAnimationState:function(o){t.splice(vn(t,{target:o}),1)},animateAll:function(o){var r=this;if(!this.options.animation){clearTimeout(e),typeof o=="function"&&o();return}var i=!1,a=0;t.forEach(function(l){var s=0,u=l.target,d=u.fromRect,c=N(u),v=u.prevFromRect,w=u.prevToRect,y=l.rect,D=be(u,!0);D&&(c.top-=D.f,c.left-=D.e),u.toRect=c,u.thisAnimationDuration&&Ze(v,c)&&!Ze(d,c)&&(y.top-c.top)/(y.left-c.left)===(d.top-c.top)/(d.left-c.left)&&(s=Sn(y,v,w,r.options)),Ze(c,d)||(u.prevFromRect=d,u.prevToRect=c,s||(s=r.options.animation),r.animate(u,y,c,s)),s&&(i=!0,a=Math.max(a,s),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},s),u.thisAnimationDuration=s)}),clearTimeout(e),i?e=setTimeout(function(){typeof o=="function"&&o()},a):typeof o=="function"&&o(),t=[]},animate:function(o,r,i,a){if(a){h(o,"transition",""),h(o,"transform","");var l=be(this.el),s=l&&l.a,u=l&&l.d,d=(r.left-i.left)/(s||1),c=(r.top-i.top)/(u||1);o.animatingX=!!d,o.animatingY=!!c,h(o,"transform","translate3d("+d+"px,"+c+"px,0)"),this.forRepaintDummy=En(o),h(o,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(o,"transform","translate3d(0,0,0)"),typeof o.animated=="number"&&clearTimeout(o.animated),o.animated=setTimeout(function(){h(o,"transition",""),h(o,"transform",""),o.animated=!1,o.animatingX=!1,o.animatingY=!1},a)}}}}function En(t){return t.offsetWidth}function Sn(t,e,n,o){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))*o.animation}var we=[],Qe={initializeByDefault:!0},Oe={mount:function(e){for(var n in Qe)Qe.hasOwnProperty(n)&&!(n in e)&&(e[n]=Qe[n]);we.forEach(function(o){if(o.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),we.push(e)},pluginEvent:function(e,n,o){var r=this;this.eventCanceled=!1,o.cancel=function(){r.eventCanceled=!0};var i=e+"Global";we.forEach(function(a){n[a.pluginName]&&(n[a.pluginName][i]&&n[a.pluginName][i](ne({sortable:n},o)),n.options[a.pluginName]&&n[a.pluginName][e]&&n[a.pluginName][e](ne({sortable:n},o)))})},initializePlugins:function(e,n,o,r){we.forEach(function(l){var s=l.pluginName;if(!(!e.options[s]&&!l.initializeByDefault)){var u=new l(e,n,e.options);u.sortable=e,u.options=e.options,e[s]=u,ie(o,u.defaults)}});for(var i in e.options)if(e.options.hasOwnProperty(i)){var a=this.modifyOption(e,i,e.options[i]);typeof a!="undefined"&&(e.options[i]=a)}},getEventProperties:function(e,n){var o={};return we.forEach(function(r){typeof r.eventProperties=="function"&&ie(o,r.eventProperties.call(n[r.pluginName],e))}),o},modifyOption:function(e,n,o){var r;return we.forEach(function(i){e[i.pluginName]&&i.optionListeners&&typeof i.optionListeners[n]=="function"&&(r=i.optionListeners[n].call(e[i.pluginName],o))}),r}};function Dn(t){var e=t.sortable,n=t.rootEl,o=t.name,r=t.targetEl,i=t.cloneEl,a=t.toEl,l=t.fromEl,s=t.oldIndex,u=t.newIndex,d=t.oldDraggableIndex,c=t.newDraggableIndex,v=t.originalEvent,w=t.putSortable,y=t.extraEventProperties;if(e=e||n&&n[$],!!e){var D,G=e.options,j="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!le&&!_e?D=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(D=document.createEvent("Event"),D.initEvent(o,!0,!0)),D.to=a||n,D.from=l||n,D.item=r||n,D.clone=i,D.oldIndex=s,D.newIndex=u,D.oldDraggableIndex=d,D.newDraggableIndex=c,D.originalEvent=v,D.pullMode=w?w.lastPutMode:void 0;var X=ne(ne({},y),Oe.getEventProperties(o,e));for(var P in X)D[P]=X[P];n&&n.dispatchEvent(D),G[j]&&G[j].call(e,D)}}var _n=["evt"],V=function(e,n){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=o.evt,i=pn(o,_n);Oe.pluginEvent.bind(p)(e,n,ne({dragEl:f,parentEl:A,ghostEl:m,rootEl:T,nextEl:he,lastDownEl:Xe,cloneEl:I,cloneHidden:fe,dragStarted:Ae,putSortable:k,activeSortable:p.active,originalEvent:r,oldIndex:Ee,oldDraggableIndex:Ie,newIndex:q,newDraggableIndex:ce,hideGhostForTarget:Lt,unhideGhostForTarget:Wt,cloneNowHidden:function(){fe=!0},cloneNowShown:function(){fe=!1},dispatchSortableEvent:function(l){W({sortable:n,name:l,originalEvent:r})}},i))};function W(t){Dn(ne({putSortable:k,cloneEl:I,targetEl:f,rootEl:T,oldIndex:Ee,oldDraggableIndex:Ie,newIndex:q,newDraggableIndex:ce},t))}var f,A,m,T,he,Xe,I,fe,Ee,q,Ie,ce,Ye,k,Se=!1,Be=!1,ke=[],pe,te,et,tt,Rt,Xt,Ae,De,xe,Pe=!1,He=!1,Le,H,nt=[],ot=!1,We=[],Ge=typeof document!="undefined",je=_t,Yt=_e||le?"cssFloat":"float",Tn=Ge&&!Tt&&!_t&&"draggable"in document.createElement("div"),Bt=function(){if(Ge){if(le)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto",t.style.pointerEvents==="auto"}}(),kt=function(e,n){var o=h(e),r=parseInt(o.width)-parseInt(o.paddingLeft)-parseInt(o.paddingRight)-parseInt(o.borderLeftWidth)-parseInt(o.borderRightWidth),i=ye(e,0,n),a=ye(e,1,n),l=i&&h(i),s=a&&h(a),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+N(i).width,d=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+N(a).width;if(o.display==="flex")return o.flexDirection==="column"||o.flexDirection==="column-reverse"?"vertical":"horizontal";if(o.display==="grid")return o.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&l.float&&l.float!=="none"){var c=l.float==="left"?"left":"right";return a&&(s.clear==="both"||s.clear===c)?"vertical":"horizontal"}return i&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||u>=r&&o[Yt]==="none"||a&&o[Yt]==="none"&&u+d>r)?"vertical":"horizontal"},Cn=function(e,n,o){var r=o?e.left:e.top,i=o?e.right:e.bottom,a=o?e.width:e.height,l=o?n.left:n.top,s=o?n.right:n.bottom,u=o?n.width:n.height;return r===l||i===s||r+a/2===l+u/2},On=function(e,n){var o;return ke.some(function(r){var i=r[$].options.emptyInsertThreshold;if(!(!i||Je(r))){var a=N(r),l=e>=a.left-i&&e<=a.right+i,s=n>=a.top-i&&n<=a.bottom+i;if(l&&s)return o=r}}),o},Ht=function(e){function n(i,a){return function(l,s,u,d){var c=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(i==null&&(a||c))return!0;if(i==null||i===!1)return!1;if(a&&i==="clone")return i;if(typeof i=="function")return n(i(l,s,u,d),a)(l,s,u,d);var v=(a?l:s).options.group.name;return i===!0||typeof i=="string"&&i===v||i.join&&i.indexOf(v)>-1}}var o={},r=e.group;(!r||Fe(r)!="object")&&(r={name:r}),o.name=r.name,o.checkPull=n(r.pull,!0),o.checkPut=n(r.put),o.revertClone=r.revertClone,e.group=o},Lt=function(){!Bt&&m&&h(m,"display","none")},Wt=function(){!Bt&&m&&h(m,"display","")};Ge&&!Tt&&document.addEventListener("click",function(t){if(Be)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),Be=!1,!1},!0);var ge=function(e){if(f){e=e.touches?e.touches[0]:e;var n=On(e.clientX,e.clientY);if(n){var o={};for(var r in e)e.hasOwnProperty(r)&&(o[r]=e[r]);o.target=o.rootEl=n,o.preventDefault=void 0,o.stopPropagation=void 0,n[$]._onDragOver(o)}}},In=function(e){f&&f.parentNode[$]._isOutsideThisEl(e.target)};function p(t,e){if(!(t&&t.nodeType&&t.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=ie({},e),t[$]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return kt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,l){a.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:p.supportPointer!==!1&&"PointerEvent"in window&&!Te,emptyInsertThreshold:5};Oe.initializePlugins(this,t,n);for(var o in n)!(o in e)&&(e[o]=n[o]);Ht(e);for(var r in this)r.charAt(0)==="_"&&typeof this[r]=="function"&&(this[r]=this[r].bind(this));this.nativeDraggable=e.forceFallback?!1:Tn,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?_(t,"pointerdown",this._onTapStart):(_(t,"mousedown",this._onTapStart),_(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(_(t,"dragover",this),_(t,"dragenter",this)),ke.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),ie(this,wn())}p.prototype={constructor:p,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(De=null)},_getDirection:function(e,n){return typeof this.options.direction=="function"?this.options.direction.call(this,e,n,f):this.options.direction},_onTapStart:function(e){if(e.cancelable){var n=this,o=this.el,r=this.options,i=r.preventOnFilter,a=e.type,l=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,s=(l||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,d=r.filter;if(Xn(o),!f&&!(/mousedown|pointerdown/.test(a)&&e.button!==0||r.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&Te&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=ee(s,r.draggable,o,!1),!(s&&s.animated)&&Xe!==s)){if(Ee=Z(s),Ie=Z(s,r.draggable),typeof d=="function"){if(d.call(this,e,s,this)){W({sortable:n,rootEl:u,name:"filter",targetEl:s,toEl:o,fromEl:o}),V("filter",n,{evt:e}),i&&e.cancelable&&e.preventDefault();return}}else if(d&&(d=d.split(",").some(function(c){if(c=ee(u,c.trim(),o,!1),c)return W({sortable:n,rootEl:c,name:"filter",targetEl:s,fromEl:o,toEl:o}),V("filter",n,{evt:e}),!0}),d)){i&&e.cancelable&&e.preventDefault();return}r.handle&&!ee(u,r.handle,o,!1)||this._prepareDragStart(e,l,s)}}},_prepareDragStart:function(e,n,o){var r=this,i=r.el,a=r.options,l=i.ownerDocument,s;if(o&&!f&&o.parentNode===i){var u=N(o);if(T=i,f=o,A=f.parentNode,he=f.nextSibling,Xe=o,Ye=a.group,p.dragged=f,pe={target:f,clientX:(n||e).clientX,clientY:(n||e).clientY},Rt=pe.clientX-u.left,Xt=pe.clientY-u.top,this._lastX=(n||e).clientX,this._lastY=(n||e).clientY,f.style["will-change"]="all",s=function(){if(V("delayEnded",r,{evt:e}),p.eventCanceled){r._onDrop();return}r._disableDelayedDragEvents(),!Dt&&r.nativeDraggable&&(f.draggable=!0),r._triggerDragStart(e,n),W({sortable:r,name:"choose",originalEvent:e}),U(f,a.chosenClass,!0)},a.ignore.split(",").forEach(function(d){It(f,d.trim(),rt)}),_(l,"dragover",ge),_(l,"mousemove",ge),_(l,"touchmove",ge),_(l,"mouseup",r._onDrop),_(l,"touchend",r._onDrop),_(l,"touchcancel",r._onDrop),Dt&&this.nativeDraggable&&(this.options.touchStartThreshold=4,f.draggable=!0),V("delayStart",this,{evt:e}),a.delay&&(!a.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(_e||le))){if(p.eventCanceled){this._onDrop();return}_(l,"mouseup",r._disableDelayedDrag),_(l,"touchend",r._disableDelayedDrag),_(l,"touchcancel",r._disableDelayedDrag),_(l,"mousemove",r._delayedDragTouchMoveHandler),_(l,"touchmove",r._delayedDragTouchMoveHandler),a.supportPointer&&_(l,"pointermove",r._delayedDragTouchMoveHandler),r._dragStartTimer=setTimeout(s,a.delay)}else s()}},_delayedDragTouchMoveHandler:function(e){var n=e.touches?e.touches[0]:e;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){f&&rt(f),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;S(e,"mouseup",this._disableDelayedDrag),S(e,"touchend",this._disableDelayedDrag),S(e,"touchcancel",this._disableDelayedDrag),S(e,"mousemove",this._delayedDragTouchMoveHandler),S(e,"touchmove",this._delayedDragTouchMoveHandler),S(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,n){n=n||e.pointerType=="touch"&&e,!this.nativeDraggable||n?this.options.supportPointer?_(document,"pointermove",this._onTouchMove):n?_(document,"touchmove",this._onTouchMove):_(document,"mousemove",this._onTouchMove):(_(f,"dragend",this),_(T,"dragstart",this._onDragStart));try{document.selection?Ve(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(o){}},_dragStarted:function(e,n){if(Se=!1,T&&f){V("dragStarted",this,{evt:n}),this.nativeDraggable&&_(document,"dragover",In);var o=this.options;!e&&U(f,o.dragClass,!1),U(f,o.ghostClass,!0),p.active=this,e&&this._appendGhost(),W({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if(te){this._lastX=te.clientX,this._lastY=te.clientY,Lt();for(var e=document.elementFromPoint(te.clientX,te.clientY),n=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(te.clientX,te.clientY),e!==n);)n=e;if(f.parentNode[$]._isOutsideThisEl(e),n)do{if(n[$]){var o=void 0;if(o=n[$]._onDragOver({clientX:te.clientX,clientY:te.clientY,target:e,rootEl:n}),o&&!this.options.dragoverBubble)break}e=n}while(n=n.parentNode);Wt()}},_onTouchMove:function(e){if(pe){var n=this.options,o=n.fallbackTolerance,r=n.fallbackOffset,i=e.touches?e.touches[0]:e,a=m&&be(m,!0),l=m&&a&&a.a,s=m&&a&&a.d,u=je&&H&&xt(H),d=(i.clientX-pe.clientX+r.x)/(l||1)+(u?u[0]-nt[0]:0)/(l||1),c=(i.clientY-pe.clientY+r.y)/(s||1)+(u?u[1]-nt[1]:0)/(s||1);if(!p.active&&!Se){if(o&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<o)return;this._onDragStart(e,!0)}if(m){a?(a.e+=d-(et||0),a.f+=c-(tt||0)):a={a:1,b:0,c:0,d:1,e:d,f:c};var v="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(m,"webkitTransform",v),h(m,"mozTransform",v),h(m,"msTransform",v),h(m,"transform",v),et=d,tt=c,te=i}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!m){var e=this.options.fallbackOnBody?document.body:T,n=N(f,!0,je,!0,e),o=this.options;if(je){for(H=e;h(H,"position")==="static"&&h(H,"transform")==="none"&&H!==document;)H=H.parentNode;H!==document.body&&H!==document.documentElement?(H===document&&(H=oe()),n.top+=H.scrollTop,n.left+=H.scrollLeft):H=oe(),nt=xt(H)}m=f.cloneNode(!0),U(m,o.ghostClass,!1),U(m,o.fallbackClass,!0),U(m,o.dragClass,!0),h(m,"transition",""),h(m,"transform",""),h(m,"box-sizing","border-box"),h(m,"margin",0),h(m,"top",n.top),h(m,"left",n.left),h(m,"width",n.width),h(m,"height",n.height),h(m,"opacity","0.8"),h(m,"position",je?"absolute":"fixed"),h(m,"zIndex","100000"),h(m,"pointerEvents","none"),p.ghost=m,e.appendChild(m),h(m,"transform-origin",Rt/parseInt(m.style.width)*100+"% "+Xt/parseInt(m.style.height)*100+"%")}},_onDragStart:function(e,n){var o=this,r=e.dataTransfer,i=o.options;if(V("dragStart",this,{evt:e}),p.eventCanceled){this._onDrop();return}V("setupClone",this),p.eventCanceled||(I=Mt(f),I.removeAttribute("id"),I.draggable=!1,I.style["will-change"]="",this._hideClone(),U(I,this.options.chosenClass,!1),p.clone=I),o.cloneId=Ve(function(){V("clone",o),!p.eventCanceled&&(o.options.removeCloneOnHide||T.insertBefore(I,f),o._hideClone(),W({sortable:o,name:"clone"}))}),!n&&U(f,i.dragClass,!0),n?(Be=!0,o._loopId=setInterval(o._emulateDragOver,50)):(S(document,"mouseup",o._onDrop),S(document,"touchend",o._onDrop),S(document,"touchcancel",o._onDrop),r&&(r.effectAllowed="move",i.setData&&i.setData.call(o,r,f)),_(document,"drop",o),h(f,"transform","translateZ(0)")),Se=!0,o._dragStartId=Ve(o._dragStarted.bind(o,n,e)),_(document,"selectstart",o),Ae=!0,Te&&h(document.body,"user-select","none")},_onDragOver:function(e){var n=this.el,o=e.target,r,i,a,l=this.options,s=l.group,u=p.active,d=Ye===s,c=l.sort,v=k||u,w,y=this,D=!1;if(ot)return;function G(me,zn){V(me,y,ne({evt:e,isOwner:d,axis:w?"vertical":"horizontal",revert:a,dragRect:r,targetRect:i,canSort:c,fromSortable:v,target:o,completed:X,onMove:function(Kt,Vn){return ze(T,n,f,r,Kt,N(Kt),e,Vn)},changed:P},zn))}function j(){G("dragOverAnimationCapture"),y.captureAnimationState(),y!==v&&v.captureAnimationState()}function X(me){return G("dragOverCompleted",{insertion:me}),me&&(d?u._hideClone():u._showClone(y),y!==v&&(U(f,k?k.options.ghostClass:u.options.ghostClass,!1),U(f,l.ghostClass,!0)),k!==y&&y!==p.active?k=y:y===p.active&&k&&(k=null),v===y&&(y._ignoreWhileAnimating=o),y.animateAll(function(){G("dragOverAnimationComplete"),y._ignoreWhileAnimating=null}),y!==v&&(v.animateAll(),v._ignoreWhileAnimating=null)),(o===f&&!f.animated||o===n&&!o.animated)&&(De=null),!l.dragoverBubble&&!e.rootEl&&o!==document&&(f.parentNode[$]._isOutsideThisEl(e.target),!me&&ge(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),D=!0}function P(){q=Z(f),ce=Z(f,l.draggable),W({sortable:y,name:"change",toEl:n,newIndex:q,newDraggableIndex:ce,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),o=ee(o,l.draggable,n,!0),G("dragOver"),p.eventCanceled)return D;if(f.contains(e.target)||o.animated&&o.animatingX&&o.animatingY||y._ignoreWhileAnimating===o)return X(!1);if(Be=!1,u&&!l.disabled&&(d?c||(a=A!==T):k===this||(this.lastPutMode=Ye.checkPull(this,u,f,e))&&s.checkPut(this,u,f,e))){if(w=this._getDirection(e,o)==="vertical",r=N(f),G("dragOverValid"),p.eventCanceled)return D;if(a)return A=T,j(),this._hideClone(),G("revert"),p.eventCanceled||(he?T.insertBefore(f,he):T.appendChild(f)),X(!0);var z=Je(n,l.draggable);if(!z||Nn(e,w,this)&&!z.animated){if(z===f)return X(!1);if(z&&n===e.target&&(o=z),o&&(i=N(o)),ze(T,n,f,r,o,i,e,!!o)!==!1)return j(),z&&z.nextSibling?n.insertBefore(f,z.nextSibling):n.appendChild(f),A=n,P(),X(!0)}else if(z&&Pn(e,w,this)){var se=ye(n,0,l,!0);if(se===f)return X(!1);if(o=se,i=N(o),ze(T,n,f,r,o,i,e,!1)!==!1)return j(),n.insertBefore(f,se),A=n,P(),X(!0)}else if(o.parentNode===n){i=N(o);var K=0,re,b=f.parentNode!==n,E=!Cn(f.animated&&f.toRect||r,o.animated&&o.toRect||i,w),F=w?"top":"left",R=At(o,"top","top")||At(f,"top","top"),C=R?R.scrollTop:void 0;De!==o&&(re=i[F],Pe=!1,He=!E&&l.invertSwap||b),K=Mn(e,o,i,w,E?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,He,De===o);var O;if(K!==0){var Y=Z(f);do Y-=K,O=A.children[Y];while(O&&(h(O,"display")==="none"||O===m))}if(K===0||O===o)return X(!1);De=o,xe=K;var L=o.nextElementSibling,J=!1;J=K===1;var de=ze(T,n,f,r,o,i,e,J);if(de!==!1)return(de===1||de===-1)&&(J=de===1),ot=!0,setTimeout(xn,30),j(),J&&!L?n.appendChild(f):o.parentNode.insertBefore(f,J?L:o),R&&Nt(R,0,C-R.scrollTop),A=f.parentNode,re!==void 0&&!He&&(Le=Math.abs(re-N(o)[F])),P(),X(!0)}if(n.contains(f))return X(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){S(document,"mousemove",this._onTouchMove),S(document,"touchmove",this._onTouchMove),S(document,"pointermove",this._onTouchMove),S(document,"dragover",ge),S(document,"mousemove",ge),S(document,"touchmove",ge)},_offUpEvents:function(){var e=this.el.ownerDocument;S(e,"mouseup",this._onDrop),S(e,"touchend",this._onDrop),S(e,"pointerup",this._onDrop),S(e,"touchcancel",this._onDrop),S(document,"selectstart",this)},_onDrop:function(e){var n=this.el,o=this.options;if(q=Z(f),ce=Z(f,o.draggable),V("drop",this,{evt:e}),A=f&&f.parentNode,q=Z(f),ce=Z(f,o.draggable),p.eventCanceled){this._nulling();return}Se=!1,He=!1,Pe=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),it(this.cloneId),it(this._dragStartId),this.nativeDraggable&&(S(document,"drop",this),S(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Te&&h(document.body,"user-select",""),h(f,"transform",""),e&&(Ae&&(e.cancelable&&e.preventDefault(),!o.dropBubble&&e.stopPropagation()),m&&m.parentNode&&m.parentNode.removeChild(m),(T===A||k&&k.lastPutMode!=="clone")&&I&&I.parentNode&&I.parentNode.removeChild(I),f&&(this.nativeDraggable&&S(f,"dragend",this),rt(f),f.style["will-change"]="",Ae&&!Se&&U(f,k?k.options.ghostClass:this.options.ghostClass,!1),U(f,this.options.chosenClass,!1),W({sortable:this,name:"unchoose",toEl:A,newIndex:null,newDraggableIndex:null,originalEvent:e}),T!==A?(q>=0&&(W({rootEl:A,name:"add",toEl:A,fromEl:T,originalEvent:e}),W({sortable:this,name:"remove",toEl:A,originalEvent:e}),W({rootEl:A,name:"sort",toEl:A,fromEl:T,originalEvent:e}),W({sortable:this,name:"sort",toEl:A,originalEvent:e})),k&&k.save()):q!==Ee&&q>=0&&(W({sortable:this,name:"update",toEl:A,originalEvent:e}),W({sortable:this,name:"sort",toEl:A,originalEvent:e})),p.active&&((q==null||q===-1)&&(q=Ee,ce=Ie),W({sortable:this,name:"end",toEl:A,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){V("nulling",this),T=f=A=m=he=I=Xe=fe=pe=te=Ae=q=ce=Ee=Ie=De=xe=k=Ye=p.dragged=p.ghost=p.clone=p.active=null,We.forEach(function(e){e.checked=!0}),We.length=et=tt=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":f&&(this._onDragOver(e),An(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],n,o=this.el.children,r=0,i=o.length,a=this.options;r<i;r++)n=o[r],ee(n,a.draggable,this.el,!1)&&e.push(n.getAttribute(a.dataIdAttr)||Rn(n));return e},sort:function(e,n){var o={},r=this.el;this.toArray().forEach(function(i,a){var l=r.children[a];ee(l,this.options.draggable,r,!1)&&(o[i]=l)},this),n&&this.captureAnimationState(),e.forEach(function(i){o[i]&&(r.removeChild(o[i]),r.appendChild(o[i]))}),n&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,n){return ee(e,n||this.options.draggable,this.el,!1)},option:function(e,n){var o=this.options;if(n===void 0)return o[e];var r=Oe.modifyOption(this,e,n);typeof r!="undefined"?o[e]=r:o[e]=n,e==="group"&&Ht(o)},destroy:function(){V("destroy",this);var e=this.el;e[$]=null,S(e,"mousedown",this._onTapStart),S(e,"touchstart",this._onTapStart),S(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(S(e,"dragover",this),S(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),ke.splice(ke.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!fe){if(V("hideClone",this),p.eventCanceled)return;h(I,"display","none"),this.options.removeCloneOnHide&&I.parentNode&&I.parentNode.removeChild(I),fe=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(fe){if(V("showClone",this),p.eventCanceled)return;f.parentNode==T&&!this.options.group.revertClone?T.insertBefore(I,f):he?T.insertBefore(I,he):T.appendChild(I),this.options.group.revertClone&&this.animate(f,I),h(I,"display",""),fe=!1}}};function An(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}function ze(t,e,n,o,r,i,a,l){var s,u=t[$],d=u.options.onMove,c;return window.CustomEvent&&!le&&!_e?s=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(s=document.createEvent("Event"),s.initEvent("move",!0,!0)),s.to=e,s.from=t,s.dragged=n,s.draggedRect=o,s.related=r||e,s.relatedRect=i||N(e),s.willInsertAfter=l,s.originalEvent=a,t.dispatchEvent(s),d&&(c=d.call(u,s,a)),c}function rt(t){t.draggable=!1}function xn(){ot=!1}function Pn(t,e,n){var o=N(ye(n.el,0,n.options,!0)),r=Ft(n.el,n.options,m),i=10;return e?t.clientX<r.left-i||t.clientY<o.top&&t.clientX<o.right:t.clientY<r.top-i||t.clientY<o.bottom&&t.clientX<o.left}function Nn(t,e,n){var o=N(Je(n.el,n.options.draggable)),r=Ft(n.el,n.options,m),i=10;return e?t.clientX>r.right+i||t.clientY>o.bottom&&t.clientX>o.left:t.clientY>r.bottom+i||t.clientX>o.right&&t.clientY>o.top}function Mn(t,e,n,o,r,i,a,l){var s=o?t.clientY:t.clientX,u=o?n.height:n.width,d=o?n.top:n.left,c=o?n.bottom:n.right,v=!1;if(!a){if(l&&Le<u*r){if(!Pe&&(xe===1?s>d+u*i/2:s<c-u*i/2)&&(Pe=!0),Pe)v=!0;else if(xe===1?s<d+Le:s>c-Le)return-xe}else if(s>d+u*(1-r)/2&&s<c-u*(1-r)/2)return Fn(e)}return v=v||a,v&&(s<d+u*i/2||s>c-u*i/2)?s>d+u/2?1:-1:0}function Fn(t){return Z(f)<Z(t)?1:-1}function Rn(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}function Xn(t){We.length=0;for(var e=t.getElementsByTagName("input"),n=e.length;n--;){var o=e[n];o.checked&&We.push(o)}}function Ve(t){return setTimeout(t,0)}function it(t){return clearTimeout(t)}Ge&&_(document,"touchmove",function(t){(p.active||Se)&&t.cancelable&&t.preventDefault()}),p.utils={on:_,off:S,css:h,find:It,is:function(e,n){return!!ee(e,n,e,!1)},extend:bn,throttle:Pt,closest:ee,toggleClass:U,clone:Mt,index:Z,nextTick:Ve,cancelNextTick:it,detectDirection:kt,getChild:ye},p.get=function(t){return t[$]},p.mount=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e[0].constructor===Array&&(e=e[0]),e.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(p.utils=ne(ne({},p.utils),o.utils)),Oe.mount(o)})},p.create=function(t,e){return new p(t,e)},p.version=gn;var M=[],Ne,at,lt=!1,st,ut,Ue,Me;function Yn(){function t(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return t.prototype={dragStarted:function(n){var o=n.originalEvent;this.sortable.nativeDraggable?_(document,"dragover",this._handleAutoScroll):this.options.supportPointer?_(document,"pointermove",this._handleFallbackAutoScroll):o.touches?_(document,"touchmove",this._handleFallbackAutoScroll):_(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var o=n.originalEvent;!this.options.dragOverBubble&&!o.rootEl&&this._handleAutoScroll(o)},drop:function(){this.sortable.nativeDraggable?S(document,"dragover",this._handleAutoScroll):(S(document,"pointermove",this._handleFallbackAutoScroll),S(document,"touchmove",this._handleFallbackAutoScroll),S(document,"mousemove",this._handleFallbackAutoScroll)),Gt(),$e(),yn()},nulling:function(){Ue=at=Ne=lt=Me=st=ut=null,M.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,o){var r=this,i=(n.touches?n.touches[0]:n).clientX,a=(n.touches?n.touches[0]:n).clientY,l=document.elementFromPoint(i,a);if(Ue=n,o||this.options.forceAutoScrollFallback||_e||le||Te){ft(n,this.options,l,o);var s=ue(l,!0);lt&&(!Me||i!==st||a!==ut)&&(Me&&Gt(),Me=setInterval(function(){var u=ue(document.elementFromPoint(i,a),!0);u!==s&&(s=u,$e()),ft(n,r.options,u,o)},10),st=i,ut=a)}else{if(!this.options.bubbleScroll||ue(l,!0)===oe()){$e();return}ft(n,this.options,ue(l,!1),!1)}}},ie(t,{pluginName:"scroll",initializeByDefault:!0})}function $e(){M.forEach(function(t){clearInterval(t.pid)}),M=[]}function Gt(){clearInterval(Me)}var ft=Pt(function(t,e,n,o){if(e.scroll){var r=(t.touches?t.touches[0]:t).clientX,i=(t.touches?t.touches[0]:t).clientY,a=e.scrollSensitivity,l=e.scrollSpeed,s=oe(),u=!1,d;at!==n&&(at=n,$e(),Ne=e.scroll,d=e.scrollFn,Ne===!0&&(Ne=ue(n,!0)));var c=0,v=Ne;do{var w=v,y=N(w),D=y.top,G=y.bottom,j=y.left,X=y.right,P=y.width,z=y.height,se=void 0,K=void 0,re=w.scrollWidth,b=w.scrollHeight,E=h(w),F=w.scrollLeft,R=w.scrollTop;w===s?(se=P<re&&(E.overflowX==="auto"||E.overflowX==="scroll"||E.overflowX==="visible"),K=z<b&&(E.overflowY==="auto"||E.overflowY==="scroll"||E.overflowY==="visible")):(se=P<re&&(E.overflowX==="auto"||E.overflowX==="scroll"),K=z<b&&(E.overflowY==="auto"||E.overflowY==="scroll"));var C=se&&(Math.abs(X-r)<=a&&F+P<re)-(Math.abs(j-r)<=a&&!!F),O=K&&(Math.abs(G-i)<=a&&R+z<b)-(Math.abs(D-i)<=a&&!!R);if(!M[c])for(var Y=0;Y<=c;Y++)M[Y]||(M[Y]={});(M[c].vx!=C||M[c].vy!=O||M[c].el!==w)&&(M[c].el=w,M[c].vx=C,M[c].vy=O,clearInterval(M[c].pid),(C!=0||O!=0)&&(u=!0,M[c].pid=setInterval(function(){o&&this.layer===0&&p.active._onTouchMove(Ue);var L=M[this.layer].vy?M[this.layer].vy*l:0,J=M[this.layer].vx?M[this.layer].vx*l:0;typeof d=="function"&&d.call(p.dragged.parentNode[$],J,L,t,Ue,M[this.layer].el)!=="continue"||Nt(M[this.layer].el,J,L)}.bind({layer:c}),24))),c++}while(e.bubbleScroll&&v!==s&&(v=ue(v,!1)));lt=u}},30),jt=function(e){var n=e.originalEvent,o=e.putSortable,r=e.dragEl,i=e.activeSortable,a=e.dispatchSortableEvent,l=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(n){var u=o||i;l();var d=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,c=document.elementFromPoint(d.clientX,d.clientY);s(),u&&!u.el.contains(c)&&(a("spill"),this.onSpill({dragEl:r,putSortable:o}))}};function ct(){}ct.prototype={startIndex:null,dragStart:function(e){var n=e.oldDraggableIndex;this.startIndex=n},onSpill:function(e){var n=e.dragEl,o=e.putSortable;this.sortable.captureAnimationState(),o&&o.captureAnimationState();var r=ye(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(n,r):this.sortable.el.appendChild(n),this.sortable.animateAll(),o&&o.animateAll()},drop:jt},ie(ct,{pluginName:"revertOnSpill"});function dt(){}dt.prototype={onSpill:function(e){var n=e.dragEl,o=e.putSortable,r=o||this.sortable;r.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),r.animateAll()},drop:jt},ie(dt,{pluginName:"removeOnSpill"}),p.mount(new Yn),p.mount(dt,ct);function Bn(t){return t==null?t:JSON.parse(JSON.stringify(t))}function kn(t){g.getCurrentInstance()&&g.onUnmounted(t)}function Hn(t){g.getCurrentInstance()?g.onMounted(t):g.nextTick(t)}let zt=null,Vt=null;function Ut(t=null,e=null){zt=t,Vt=e}function Ln(){return{data:zt,clonedData:Vt}}const $t=Symbol("cloneElement");function ht(...t){var K,re;const e=(K=g.getCurrentInstance())==null?void 0:K.proxy;let n=null;const o=t[0];let[,r,i]=t;Array.isArray(g.unref(r))||(i=r,r=null);let a=null;const{immediate:l=!0,clone:s=Bn,customUpdate:u}=(re=g.unref(i))!=null?re:{};function d(b){var Y;const{from:E,oldIndex:F,item:R}=b;n=Array.from(E.childNodes);const C=g.unref((Y=g.unref(r))==null?void 0:Y[F]),O=s(C);Ut(C,O),R[$t]=O}function c(b){const E=b.item[$t];if(!on(E)){if(Ke(b.item),g.isRef(r)){const F=[...g.unref(r)];r.value=yt(F,b.newDraggableIndex,E);return}yt(g.unref(r),b.newDraggableIndex,E)}}function v(b){const{from:E,item:F,oldIndex:R,oldDraggableIndex:C,pullMode:O,clone:Y}=b;if(wt(E,F,R),O==="clone"){Ke(Y);return}if(g.isRef(r)){const L=[...g.unref(r)];r.value=bt(L,C);return}bt(g.unref(r),C)}function w(b){if(u){u(b);return}const{from:E,item:F,oldIndex:R,oldDraggableIndex:C,newDraggableIndex:O}=b;if(Ke(F),wt(E,F,R),g.isRef(r)){const Y=[...g.unref(r)];r.value=vt(Y,C,O);return}vt(g.unref(r),C,O)}function y(b){const{newIndex:E,oldIndex:F,from:R,to:C}=b;let O=null;const Y=E===F&&R===C;try{if(Y){let L=null;n==null||n.some((J,de)=>{if(L&&(n==null?void 0:n.length)!==C.childNodes.length)return R.insertBefore(L,J.nextSibling),!0;const me=C.childNodes[de];L=C==null?void 0:C.replaceChild(J,me)})}}catch(L){O=L}finally{n=null}g.nextTick(()=>{if(Ut(),O)throw O})}const D={onUpdate:w,onStart:d,onAdd:c,onRemove:v,onEnd:y};function G(b){const E=g.unref(o);return b||(b=rn(E)?an(E,e==null?void 0:e.$el):E),b&&!un(b)&&(b=b.$el),b||en("Root element not found"),b}function j(){var R;const C=(R=g.unref(i))!=null?R:{},{immediate:b,clone:E}=C,F=mt(C,["immediate","clone"]);return Et(F,(O,Y)=>{fn(O)&&(F[O]=(L,...J)=>{const de=Ln();return cn(L,de),Y(L,...J)})}),sn(r===null?{}:D,F)}const X=b=>{b=G(b),a&&P.destroy(),a=new p(b,j())};g.watch(()=>i,()=>{a&&Et(j(),(b,E)=>{a==null||a.option(b,E)})},{deep:!0});const P={option:(b,E)=>a==null?void 0:a.option(b,E),destroy:()=>{a==null||a.destroy(),a=null},save:()=>a==null?void 0:a.save(),toArray:()=>a==null?void 0:a.toArray(),closest:(...b)=>a==null?void 0:a.closest(...b)},z=()=>P==null?void 0:P.option("disabled",!0),se=()=>P==null?void 0:P.option("disabled",!1);return Hn(()=>{l&&X()}),kn(P.destroy),ve({start:X,pause:z,resume:se},P)}const pt=["update","start","add","remove","choose","unchoose","end","sort","filter","clone","move","change"],Wn=["clone","animation","ghostClass","group","sort","disabled","store","handle","draggable","swapThreshold","invertSwap","invertedSwapThreshold","removeCloneOnHide","direction","chosenClass","dragClass","ignore","filter","preventOnFilter","easing","setData","dropBubble","dragoverBubble","dataIdAttr","delay","delayOnTouchOnly","touchStartThreshold","forceFallback","fallbackClass","fallbackOnBody","fallbackTolerance","fallbackOffset","supportPointer","emptyInsertThreshold","scroll","forceAutoScrollFallback","scrollSensitivity","scrollSpeed","bubbleScroll","modelValue","tag","target","customUpdate",...pt.map(t=>`on${t.replace(/^\S/,e=>e.toUpperCase())}`)],Gn=g.defineComponent({name:"VueDraggable",model:{prop:"modelValue",event:"update:modelValue"},props:Wn,emits:["update:modelValue",...pt],setup(t,{slots:e,emit:n,expose:o,attrs:r}){const i=pt.reduce((d,c)=>{const v=`on${c.replace(/^\S/,w=>w.toUpperCase())}`;return d[v]=(...w)=>n(c,...w),d},{}),a=g.computed(()=>{const w=g.toRefs(t),{modelValue:d}=w,c=mt(w,["modelValue"]),v=Object.entries(c).reduce((y,[D,G])=>{const j=g.unref(G);return j!==void 0&&(y[D]=j),y},{});return ve(ve({},i),nn(ve(ve({},r),v)))}),l=g.computed({get:()=>t.modelValue,set:d=>n("update:modelValue",d)}),s=g.ref(),u=g.reactive(ht(t.target||s,l,a));return o(u),()=>{var d;return g.h(t.tag||"div",{ref:s},(d=e==null?void 0:e.default)==null?void 0:d.call(e,u))}}}),qt={mounted:"mounted",unmounted:"unmounted"},gt=new WeakMap,jn={[qt.mounted](t,e){const n=g.isProxy(e.value)?[e.value]:e.value,[o,r]=n,i=ht(t,o,r);gt.set(t,i.destroy)},[qt.unmounted](t){var e;(e=gt.get(t))==null||e(),gt.delete(t)}};return x.VueDraggable=Gn,x.useDraggable=ht,x.vDraggable=jn,Object.defineProperty(x,Symbol.toStringTag,{value:"Module"}),x}({},Vue);
