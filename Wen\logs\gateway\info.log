[1;35m19:19:17.818[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:19:17.908[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.<PERSON>[0;39m - [36m[logStarting,53][0;39m - Starting WenGatewayApp using Java 17.0.15 with PID 30600 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-gateway\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:19:17.908[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.<PERSON>atewayApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:19:18.481[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:19:18.482[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:19:18.599[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:19:18.602[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:19:18.625[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
[1;35m19:19:18.737[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=4269b25e-db53-35ca-84e1-9c1f4895ad02
[1;35m19:19:19.945[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m19:19:19.945[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m19:19:19.945[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m19:19:19.945[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m19:19:19.946[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m19:19:19.965[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m19:19:20.130[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 19 endpoints beneath base path '/actuator'
[1;35m19:19:20.211[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m19:19:20.780[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 20001 (http)
[1;35m19:19:21.320[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarted,59][0;39m - Started WenGatewayApp in 5.197 seconds (process running for 5.65)
[1;35m19:19:21.862[0;39m [32m[RMI TCP Connection(1)-192.168.101.3][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m19:19:40.368[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:19:40.451[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarting,53][0;39m - Starting WenGatewayApp using Java 17.0.15 with PID 4416 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-gateway\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:19:40.452[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:19:40.984[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:19:40.985[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:19:41.098[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:19:41.100[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:19:41.121[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
[1;35m19:19:41.236[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=4269b25e-db53-35ca-84e1-9c1f4895ad02
[1;35m19:19:42.459[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m19:19:42.459[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m19:19:42.459[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m19:19:42.459[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m19:19:42.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m19:19:42.480[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m19:19:42.639[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 19 endpoints beneath base path '/actuator'
[1;35m19:19:42.723[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m19:19:43.275[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 20001 (http)
[1;35m19:19:43.789[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarted,59][0;39m - Started WenGatewayApp in 5.086 seconds (process running for 5.553)
[1;35m19:19:44.125[0;39m [32m[RMI TCP Connection(4)-192.168.101.3][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m19:24:37.696[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:24:37.776[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarting,53][0;39m - Starting WenGatewayApp using Java 17.0.15 with PID 26548 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-gateway\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:24:37.777[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:24:38.307[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:24:38.307[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:24:38.414[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:24:38.417[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:24:38.438[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
[1;35m19:24:38.548[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=4269b25e-db53-35ca-84e1-9c1f4895ad02
[1;35m19:24:39.724[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m19:24:39.724[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m19:24:39.724[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m19:24:39.724[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m19:24:39.724[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m19:24:39.726[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m19:24:39.744[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m19:24:39.904[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 19 endpoints beneath base path '/actuator'
[1;35m19:24:39.982[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m19:24:40.553[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 20001 (http)
[1;35m19:24:41.100[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarted,59][0;39m - Started WenGatewayApp in 5.834 seconds (process running for 6.333)
[1;35m19:24:41.475[0;39m [32m[RMI TCP Connection(1)-192.168.101.3][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m19:25:16.565[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:25:16.655[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarting,53][0;39m - Starting WenGatewayApp using Java 17.0.15 with PID 29628 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-gateway\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:25:16.657[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:25:17.213[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:25:17.213[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:25:17.326[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:25:17.329[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:25:17.349[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
[1;35m19:25:17.462[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=4269b25e-db53-35ca-84e1-9c1f4895ad02
[1;35m19:25:18.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m19:25:18.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m19:25:18.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m19:25:18.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m19:25:18.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m19:25:18.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m19:25:18.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m19:25:18.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m19:25:18.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m19:25:18.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m19:25:18.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m19:25:18.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m19:25:18.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m19:25:18.716[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m19:25:18.733[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m19:25:18.894[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 19 endpoints beneath base path '/actuator'
[1;35m19:25:18.976[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m19:25:19.553[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 20001 (http)
[1;35m19:25:20.115[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarted,59][0;39m - Started WenGatewayApp in 5.125 seconds (process running for 5.664)
[1;35m19:25:20.639[0;39m [32m[RMI TCP Connection(2)-192.168.101.3][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m19:26:43.600[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarting,53][0;39m - Starting WenGatewayApp using Java 17.0.15 with PID 25156 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-gateway\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:26:43.602[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:26:46.966[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarted,59][0;39m - Started WenGatewayApp in 5.059 seconds (process running for 5.581)
[1;35m19:28:08.277[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:28:08.367[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarting,53][0;39m - Starting WenGatewayApp using Java 17.0.15 with PID 26004 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-gateway\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:28:08.368[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:28:08.916[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:28:08.917[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:28:09.029[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:28:09.032[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:28:09.051[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
[1;35m19:28:09.169[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=4269b25e-db53-35ca-84e1-9c1f4895ad02
[1;35m19:28:10.438[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m19:28:10.438[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m19:28:10.438[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m19:28:10.438[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m19:28:10.438[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m19:28:10.439[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m19:28:10.456[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m19:28:10.610[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 19 endpoints beneath base path '/actuator'
[1;35m19:28:10.687[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m19:28:11.339[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 20001 (http)
[1;35m19:28:11.933[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.g.WenGatewayApp[0;39m - [36m[logStarted,59][0;39m - Started WenGatewayApp in 5.289 seconds (process running for 5.84)
[1;35m19:28:12.386[0;39m [32m[RMI TCP Connection(7)-192.168.101.3][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
