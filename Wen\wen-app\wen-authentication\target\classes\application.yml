# Tomcat
server:
  port: 20002
# Spring
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    # 应用名称
    name: authentication
  profiles:
    # 环境配置
    active: ${SERVER_PROFILES:wen}
  cloud:
    inetutils:
      use-only-site-local-interfaces: true
      # 指定默认ip为本地局域网ip
      default-ip-address: ${DEFAULT_IP_ADDRESS:}
    nacos:
      server-addr: ${NACOS_IP:localhost}:${NACOS_PORT:8848}
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:shenmo@nacos}
      discovery:
        # 命名空间 -> 区分环境
        namespace: ${DISCOVERY_NAMESPACE:${spring.profiles.active:wen}}
        # 指定服务注册的ip
        ip: ${spring.cloud.inetutils.default-ip-address:}
      config:
        # 命名空间 -> 区分环境
        namespace: ${CONFIG_NAMESPACE:${spring.profiles.active:wen}}
  config:
    import:
      - optional:nacos:${spring.application.name}.yml
      - optional:nacos:server-info.yml
      - optional:nacos:server-config.yml
