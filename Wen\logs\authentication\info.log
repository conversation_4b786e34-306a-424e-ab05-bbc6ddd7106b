[1;35m19:20:07.910[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:20:07.959[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.a.WenAuthenticationApp[0;39m - [36m[logStarting,53][0;39m - Starting WenAuthenticationApp using Java 17.0.15 with PID 27656 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-authentication\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:20:07.961[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.a.WenAuthenticationApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:20:08.551[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:20:08.551[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:20:08.707[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:20:08.710[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:20:08.734[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[1;35m19:20:08.911[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=ab883aa4-03a5-3c76-9083-06db4798ed9c
[1;35m19:20:09.370[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 20002 (http)
[1;35m19:20:09.382[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-20002"]
[1;35m19:20:09.383[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m19:20:09.383[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.42]
[1;35m19:20:09.434[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m19:20:09.435[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 1443 ms
[1;35m19:20:09.625[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.s.b.a.DruidDataSourceAutoConfigure[0;39m - [36m[dataSource,68][0;39m - Init DruidDataSource
[1;35m19:20:09.680[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,824][0;39m - {dataSource-1} inited
[1;35m19:20:10.006[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.SentinelWebMvcConfigurer[0;39m - [36m[addInterceptors,52][0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[1;35m19:20:28.546[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:20:28.597[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.a.WenAuthenticationApp[0;39m - [36m[logStarting,53][0;39m - Starting WenAuthenticationApp using Java 17.0.15 with PID 30900 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-authentication\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:20:28.598[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.a.WenAuthenticationApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:20:29.186[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:20:29.186[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:20:29.343[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:20:29.346[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:20:29.370[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[1;35m19:20:29.533[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=ab883aa4-03a5-3c76-9083-06db4798ed9c
[1;35m19:20:30.007[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 20002 (http)
[1;35m19:20:30.020[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-20002"]
[1;35m19:20:30.021[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m19:20:30.021[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.42]
[1;35m19:20:30.078[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m19:20:30.078[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 1447 ms
[1;35m19:20:30.261[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.s.b.a.DruidDataSourceAutoConfigure[0;39m - [36m[dataSource,68][0;39m - Init DruidDataSource
[1;35m19:20:30.315[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,824][0;39m - {dataSource-1} inited
[1;35m19:20:30.620[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.SentinelWebMvcConfigurer[0;39m - [36m[addInterceptors,52][0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[1;35m19:20:31.227[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.m.e.s.MybatisPlusApplicationContextAware[0;39m - [36m[setApplicationContext,40][0;39m - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@60bb7995
[1;35m19:20:31.635[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 19 endpoints beneath base path '/actuator'
[1;35m19:20:31.692[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Starting ProtocolHandler ["http-nio-20002"]
[1;35m19:20:31.700[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[start,243][0;39m - Tomcat started on port 20002 (http) with context path '/'
[1;35m19:20:32.054[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.a.WenAuthenticationApp[0;39m - [36m[logStarted,59][0;39m - Started WenAuthenticationApp in 5.206 seconds (process running for 5.685)
[1;35m19:22:09.152[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m19:22:09.185[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.a.WenAuthenticationApp[0;39m - [36m[logStarting,53][0;39m - Starting WenAuthenticationApp using Java 17.0.15 with PID 30992 (C:\Users\<USER>\Desktop\shenmo\Wen\wen-app\wen-authentication\target\classes started by Administrator in C:\Users\<USER>\Desktop\shenmo\Wen)
[1;35m19:22:09.185[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.a.WenAuthenticationApp[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "wen"
[1;35m19:22:09.759[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosConfigProperties' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; factoryMethodName=nacosConfigProperties; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:22:09.760[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'nacosAnnotationProcessor' with a different definition: replacing [Root bean: class=com.alibaba.cloud.nacos.NacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/nacos/NacosConfigAutoConfiguration.class]] with [Root bean: class=com.shenmo.wen.common.config.nacos.WenNacosConfigAutoConfiguration; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=nacosAnnotationProcessor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/shenmo/wen/common/config/nacos/WenNacosConfigAutoConfiguration.class]]
[1;35m19:22:09.905[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m19:22:09.907[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m19:22:09.930[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
[1;35m19:22:10.086[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=ab883aa4-03a5-3c76-9083-06db4798ed9c
[1;35m19:22:10.515[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 20002 (http)
[1;35m19:22:10.524[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-20002"]
[1;35m19:22:10.526[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m19:22:10.527[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.42]
[1;35m19:22:10.580[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m19:22:10.580[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 1366 ms
[1;35m19:22:10.744[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.s.b.a.DruidDataSourceAutoConfigure[0;39m - [36m[dataSource,68][0;39m - Init DruidDataSource
[1;35m19:22:10.796[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,824][0;39m - {dataSource-1} inited
[1;35m19:22:11.084[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.SentinelWebMvcConfigurer[0;39m - [36m[addInterceptors,52][0;39m - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
[1;35m19:22:11.672[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.m.e.s.MybatisPlusApplicationContextAware[0;39m - [36m[setApplicationContext,40][0;39m - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@77f905e3
[1;35m19:22:12.044[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 19 endpoints beneath base path '/actuator'
[1;35m19:22:12.100[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Starting ProtocolHandler ["http-nio-20002"]
[1;35m19:22:12.108[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[start,243][0;39m - Tomcat started on port 20002 (http) with context path '/'
[1;35m19:22:12.456[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.s.w.a.a.WenAuthenticationApp[0;39m - [36m[logStarted,59][0;39m - Started WenAuthenticationApp in 4.95 seconds (process running for 5.442)
[1;35m19:22:12.913[0;39m [32m[RMI TCP Connection(1)-192.168.101.3][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring DispatcherServlet 'dispatcherServlet'
[1;35m19:22:12.914[0;39m [32m[RMI TCP Connection(1)-192.168.101.3][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,532][0;39m - Initializing Servlet 'dispatcherServlet'
[1;35m19:22:12.914[0;39m [32m[RMI TCP Connection(1)-192.168.101.3][0;39m [34mINFO [0;39m [36mo.s.w.s.DispatcherServlet[0;39m - [36m[initServletBean,554][0;39m - Completed initialization in 0 ms
[1;35m19:22:12.926[0;39m [32m[RMI TCP Connection(2)-192.168.101.3][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
