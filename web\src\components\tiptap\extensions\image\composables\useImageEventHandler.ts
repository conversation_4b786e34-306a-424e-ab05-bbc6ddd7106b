import logger from '@/utils/log'

import { handleImageUpload } from '../ImageResourceManager'

import type { EditorWithStorage } from '../useImageUpload'

/**
 * 图片事件处理组合式函数
 * 处理图片的粘贴和拖放事件
 */
export function useImageEventHandler() {
  /**
   * 处理图片粘贴、拖放事件的逻辑
   * @param editor 编辑器实例
   * @param files 文件列表
   * @param attrs 属性
   * @param fileBucket 存储桶名称
   * @param useThumbnail 是否使用缩略图
   */
  const imageHandleCallback = (
    editor: EditorWithStorage,
    files: File[],
    attrs: { src?: string; [key: string]: unknown },
    fileBucket: string,
    useThumbnail: boolean,
  ) => {
    logger.debug('imageHandleCallback called with:', {
      filesCount: files.length,
      hasAttrs: !!attrs,
    })

    // 没有文件时直接返回
    if (!files.length) {
      return
    }

    // 只处理图片文件
    const imageFiles = filterImageFiles(files)
    if (imageFiles.length === 0) {
      logger.debug('No image files found in event')
      return
    }

    // 处理图片文件
    if (shouldSkipFirstImage(attrs)) {
      // 编辑器已经在处理第一个图片，只处理额外的图片
      processAdditionalImages(imageFiles, editor, fileBucket, useThumbnail)
    } else {
      // 编辑器可能没有正确识别图片，手动处理所有图片
      processAllImages(imageFiles, editor, fileBucket, useThumbnail)
    }
  }

  /**
   * 过滤出图片文件
   * @param files 文件列表
   * @returns 图片文件数组
   */
  const filterImageFiles = (files: File[]): File[] => {
    return files.filter((file) => file.type.startsWith('image/'))
  }

  /**
   * 判断是否应该跳过第一个图片的处理
   * @param attrs 属性对象
   * @returns 是否跳过
   */
  const shouldSkipFirstImage = (attrs: { src?: string; [key: string]: unknown }): boolean => {
    return !!(attrs && attrs.src)
  }

  /**
   * 处理额外的图片文件（跳过第一个）
   * @param imageFiles 图片文件数组
   * @param editor 编辑器实例
   * @param fileBucket 存储桶名称
   * @param useThumbnail 是否使用缩略图
   */
  const processAdditionalImages = (
    imageFiles: File[],
    editor: EditorWithStorage,
    fileBucket: string,
    useThumbnail: boolean,
  ) => {
    logger.debug('Editor is already handling one image, skipping duplicate processing')

    // 只处理从第二个开始的图片文件（如果有）
    if (imageFiles.length > 1) {
      logger.debug('Processing additional images:', imageFiles.length - 1)
      for (let i = 1; i < imageFiles.length; i++) {
        handleImageUpload(imageFiles[i], editor, fileBucket, useThumbnail)
      }
    }
  }

  /**
   * 处理所有图片文件
   * @param imageFiles 图片文件数组
   * @param editor 编辑器实例
   * @param fileBucket 存储桶名称
   * @param useThumbnail 是否使用缩略图
   */
  const processAllImages = (
    imageFiles: File[],
    editor: EditorWithStorage,
    fileBucket: string,
    useThumbnail: boolean,
  ) => {
    logger.debug('Processing all images:', imageFiles.length)
    imageFiles.forEach((file) => {
      handleImageUpload(file, editor, fileBucket, useThumbnail)
    })
  }

  return {
    imageHandleCallback,
    filterImageFiles,
    shouldSkipFirstImage,
    processAdditionalImages,
    processAllImages,
  }
}
