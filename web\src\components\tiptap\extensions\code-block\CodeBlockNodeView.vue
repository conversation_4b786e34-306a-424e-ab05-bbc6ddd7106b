<template>
  <NodeViewWrapper
    as="pre"
    :class="[
      'code-block-container',
      {
        'code-wrap': wrapMode,
        'code-block-readonly': !isEditable,
        'editable-mode': isEditable,
        'readonly-mode': !isEditable,
      },
    ]"
    :data-language="language"
    :data-selectable="false"
  >
    <!-- 代码块功能栏 -->
    <CodeBlockToolbar
      :language="language"
      :wrap-mode="wrapMode"
      :copy-state="copyState"
      @toggle-wrap="toggleWrapMode"
      @copy-code="copyCode"
    />

    <!-- 代码内容区域 -->
    <div class="code-scrollbar-container" :class="{ 'code-wrap': wrapMode }">
      <NScrollbar :x-scrollable="!wrapMode" :y-scrollable="false" trigger="hover" :size="8">
        <NodeViewContent
          as="code"
          :class="[
            `language-${language}`,
            {
              'code-wrap-enabled': wrapMode,
              'code-selectable': !isEditable,
            },
          ]"
          :style="codeElementStyles"
        />
      </NScrollbar>
    </div>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper, NodeViewContent } from '@tiptap/vue-3'
import { NScrollbar } from 'naive-ui'
import { ref, computed, onMounted } from 'vue'
import type { NodeViewProps } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { Editor } from '@tiptap/core'

import { TIPTAP_CODE_COPY } from '@/constants/frequency_key.constants'
import frequencyLimit from '@/utils/frequency-limit'
import message from '@/utils/message'

import CodeBlockToolbar from './CodeBlockToolbar.vue'

// 定义组件属性
const props = defineProps<
  NodeViewProps & {
    node: ProseMirrorNode
    editor: Editor
    getPos: () => number
    updateAttributes: (attrs: Record<string, unknown>) => void
    deleteNode: () => void
    selected: boolean
    extension: {
      name: string
      options: Record<string, unknown>
    }
    HTMLAttributes: Record<string, unknown>
  }
>()

// 响应式状态
const wrapMode = ref(false)
const copyState = ref({
  copied: false,
  timer: null as number | null,
})

// 计算属性
const isEditable = computed(() => props.editor.isEditable)

const language = computed(() => {
  const lang = props.node.attrs.language || 'text'
  return lang === 'null' ? 'text' : lang
})

const codeElementStyles = computed(() => ({
  display: 'block',
  padding: '0.8rem 1rem',
  margin: '0',
  background: 'transparent',
  border: 'none',
  borderRadius: '0',
  fontFamily: 'inherit',
  fontSize: 'inherit',
  lineHeight: 'inherit',
  whiteSpace: wrapMode.value ? 'pre-wrap' : 'pre',
  wordBreak: wrapMode.value ? 'break-word' : 'normal',
  overflowWrap: wrapMode.value ? 'break-word' : 'normal',
  width: '100%',
  boxSizing: 'border-box',
}))

// 方法
const toggleWrapMode = () => {
  wrapMode.value = !wrapMode.value
}

const copyCode = async () => {
  try {
    const codeContent = props.node.textContent || ''
    await navigator.clipboard.writeText(codeContent)

    message.success('代码已复制到剪贴板')

    // 设置复制状态
    copyState.value.copied = true

    // 使用统一的防抖工具，3秒后恢复状态
    frequencyLimit.debounce(
      TIPTAP_CODE_COPY,
      () => {
        copyState.value.copied = false
      },
      3000,
    )
  } catch (error) {
    message.error('复制失败，请手动复制')
    console.error('复制失败:', error)
  }
}

// 生命周期
onMounted(() => {
  // 初始化换行模式（如果需要从属性中读取）
  if (props.node.attrs.wrap !== undefined && props.node.attrs.wrap !== null) {
    wrapMode.value = Boolean(props.node.attrs.wrap)
  }
})
</script>

<style scoped>
.code-block-container {
  position: relative;
  background-color: var(--creamy-white-1, #eeece4);
  border-radius: 4px;
  padding: 0;
  margin: 1rem 0;
  overflow: hidden;
  font-family: Consolas, 'Source Code Pro', 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  border: 1px solid var(--creamy-white-3, #dcd8ca);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 5%);
  max-width: 100%;
  width: 100%;
  
  /* 编辑模式下确保可以接收焦点和事件 */
  &.editable-mode {
    pointer-events: auto;
    
    :deep(.n-scrollbar-content) {
      pointer-events: auto;
    }
    
    :deep(code[contenteditable="true"]) {
      pointer-events: auto;
      cursor: text;
      
      &:focus {
        outline: none;
        box-shadow: none;
      }
    }
  }
}

/* 工具栏样式已移至 CodeBlockToolbar.vue */

.code-scrollbar-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  position: relative;
  min-height: 3rem;
}

/* NScrollbar 样式 */
:deep(.n-scrollbar) {
  width: 100%;
  height: auto;
  max-width: 100%;
  box-sizing: border-box;
}

/* 非换行模式：内容宽度自适应，允许水平滚动 */
:not(.code-wrap) :deep(.n-scrollbar-content) {
  width: max-content;
  min-width: 100%;
  max-width: none; /* 允许内容超出以触发滚动 */
}

/* 换行模式：内容宽度100%，禁用水平滚动 */
.code-wrap :deep(.n-scrollbar-content) {
  width: 100%;
  max-width: 100%;
}

/* 水平滚动条轨道 - 透明背景 */
:deep(.n-scrollbar-rail--horizontal) {
  height: 8px;
  background-color: transparent;
  border-radius: 4px;
  bottom: 2px;

  /* 强制启用滚动条 */
  &.n-scrollbar-rail--disabled {
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
  }
}

/* 水平滚动条滑块 */
:deep(.n-scrollbar-thumb--horizontal) {
  background-color: var(--gray-4, rgba(53, 38, 28, 40%));
  border-radius: 4px;
  transition: background-color 0.2s ease;
  min-width: 20px;
  height: 6px;
}

:deep(.n-scrollbar-thumb--horizontal:hover) {
  background-color: var(--gray-5, rgba(28, 25, 23, 60%));
}

/* 换行模式下隐藏水平滚动条 */
.code-wrap :deep(.n-scrollbar-rail--horizontal) {
  display: none !important;
}
</style>
