<template>
  <NodeViewWrapper class="bilibili-container">
    <div class="bilibili-wrapper">
      <iframe
        v-if="iframeSrc"
        :src="iframeSrc"
        scrolling="no"
        border="0"
        frameborder="0"
        framespacing="0"
        allowfullscreen="true"
        :data-danmaku="node.attrs.danmaku"
        :data-mute="node.attrs.mute"
        class="bilibili-iframe"
      />
      <div v-else class="bilibili-error">
        <p>无效的 Bilibili 视频链接</p>
      </div>
    </div>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NodeViewWrapper } from '@tiptap/vue-3'
import type { NodeViewProps } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'

interface Props extends NodeViewProps {
  node: ProseMirrorNode & {
    attrs: {
      src: string
      danmaku: boolean
      mute: boolean
    }
  }
}

const props = defineProps<Props>()

/**
 * B站链接解析函数
 * 解析 Bilibili 视频 URL，提取视频 ID
 */
const parseBilibiliUrl = (url: string): string | null => {
  if (!url) return null

  // 支持 BV 号
  const bvidPattern = /(BV[a-zA-Z0-9]{10})/
  // 支持 aid 号
  const aidPattern = /aid=(\d+)/
  // 支持短链接
  const shortPattern = /b23\.tv\/([a-zA-Z0-9]+)/
  // 支持完整链接
  const fullPattern = /bilibili\.com\/video\/([a-zA-Z0-9]+)/

  const bvidMatch = url.match(bvidPattern)
  const aidMatch = url.match(aidPattern)
  const shortMatch = url.match(shortPattern)
  const fullMatch = url.match(fullPattern)

  return bvidMatch
    ? bvidMatch[1]
    : aidMatch
      ? aidMatch[1]
      : shortMatch
        ? shortMatch[1]
        : fullMatch
          ? fullMatch[1]
          : null
}

/**
 * 计算 iframe 源地址
 */
const iframeSrc = computed(() => {
  const src = parseBilibiliUrl(props.node.attrs.src)
  if (!src) return null

  const params = new URLSearchParams({
    [src.startsWith('BV') ? 'bvid' : 'aid']: src,
    page: '1',
    danmaku: props.node.attrs.danmaku ? '1' : '0',
    mute: props.node.attrs.mute ? '1' : '0',
    high_quality: '1',
  })

  return `//www.bilibili.com/blackboard/html5mobileplayer.html?${params.toString()}`
})
</script>

<style scoped>
.bilibili-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.bilibili-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: var(--color-border-light);
  color: var(--color-text-secondary);
  border-radius: 4px;
}

.bilibili-error p {
  margin: 0;
  font-size: 14px;
}

/* 全局样式已经排除了 bilibili-container，不再需要覆盖选中状态 */
.bilibili-container {
  outline: none;
  border: none;
}
</style>
