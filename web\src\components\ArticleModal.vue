<template>
  <NModal
    v-model:show="isArticleDialogVisible"
    preset="dialog"
    negative-text="算了"
    positive-text="确认"
    @negative-click="handleClose"
    @positive-click="submitArticleForm"
    :showIcon="false"
    @close="handleClose"
    @mask-click="handleClose"
    :mask-closable="false"
    :auto-focus="false"
    :close-on-esc="false"
    class="article-modal"
    :positive-button-props="{ loading: submitLoading }"
  >
    <template #header>
      <NGradientText type="primary" :size="20">
        {{ isEditingArticle ? '是得再改改' : '想点什么呢' }}
      </NGradientText>
      <NIcon size="24" class="cursor-pointer" @click="handleArticleFileClick">
        <FileUpload />
      </NIcon>
      <input
        type="file"
        ref="articleFileInputRef"
        accept=".md"
        @change="handleArticleFileChange"
        class="display-none"
      />
    </template>
    <NForm :model="articleForm" ref="articleFormRef" label-placement="left">
      <!-- 标题 -->
      <NFormItem label="标题" path="title" style="width: min(30rem, 100%)">
        <NInput v-model:value="articleForm.title" placeholder="请输入文章标题" />
      </NFormItem>
      <!-- 标签 -->
      <NFormItem label="标签" path="tag" style="width: min(30rem, 100%)">
        <NDynamicTags
          v-model:value="articleForm.tags"
          :input-props="{ maxlength: 20 }"
          :max="3"
          type="primary"
          placeholder="请输入标签"
        />
      </NFormItem>
      <div class="flex-between-center" style="width: min(16rem, 100%)">
        <!-- 等级 -->
        <NFormItem label="等级 | 范围" path="allowCommentLevel" style="width: 6rem">
          <NPopselect
            v-model:value="articleForm.operationLevel"
            :options="generateCommentLevel"
            size="small"
            trigger="click"
          >
            <NButton size="small"> Lv{{ articleForm.operationLevel || '0' }} </NButton>
          </NPopselect>
        </NFormItem>
        <!-- 范围 -->
        <NFormItem path="scope">
          <NRadioGroup
            v-model:value="articleForm.publishedScope"
            size="small"
            :default-value="ArticlePublishedScope.PERSONAL"
          >
            <NRadioButton
              class="flex-between-center"
              v-for="scope in [
                {
                  value: ArticlePublishedScope.PUBLIC,
                  label: ARTICLE_PUBLISHED_SCOPE_LABEL[ArticlePublishedScope.PUBLIC],
                },
                {
                  value: ArticlePublishedScope.PERSONAL,
                  label: ARTICLE_PUBLISHED_SCOPE_LABEL[ArticlePublishedScope.PERSONAL],
                },
              ]"
              :key="scope.value"
              :value="scope.value"
              :label="scope.label"
            />
          </NRadioGroup>
        </NFormItem>
      </div>
      <!-- 分享用户，仅个人范围时显示 -->
      <NFormItem
        v-if="articleForm.publishedScope === ArticlePublishedScope.PERSONAL"
        label="分享给"
        path="shareUsers"
        style="width: min(30rem, 100%)"
      >
        <SearchUserSelect
          v-model="articleForm.shareUsers"
          placeholder="请搜索并选择用户"
          ref="shareUserSelectRef"
        />
      </NFormItem>
      <!-- 内容 -->
      <NFormItem path="content">
        <NScrollbar class="article-modal-content">
          <TiptapEditor
            ref="articleTiptapEditorRef"
            v-model="articleForm.contentObj"
            :editor-props="{
              attributes: {
                class: 'ProseMirrorNoneOutline',
              },
            }"
            :bubble-menu="true"
            :floating-menu="true"
            :file-bucket="ARTICLE"
            :all-extensions="true"
            :toolbar="true"
            :toolbar-class="['editor-toolbar', 'editor-toolbar-bgc']"
            placeholder="尽情发挥！"
            :show-character-count="true"
            :character-limit="ARTICLE_CHARACTER_LIMIT"
            :save-loading="quickSaveLoading"
            @save="quickSaveArticleForm"
          />
        </NScrollbar>
      </NFormItem>
    </NForm>
  </NModal>
</template>

<script setup lang="tsx">
import { FileUpload } from '@vicons/tabler'
import {
  NButton,
  NIcon,
  NModal,
  NForm,
  NFormItem,
  NPopselect,
  NInput,
  NDynamicTags,
  NGradientText,
  NScrollbar,
  NRadioGroup,
  NRadioButton,
} from 'naive-ui'
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

import articleApi from '@/api/article'
import SearchUserSelect from '@/components/SearchUserSelect.vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import {
  ArticlePublishedScope,
  ARTICLE_PUBLISHED_SCOPE_LABEL,
} from '@/constants/article_published_scope.constants'
import { ARTICLE } from '@/constants/bucket.constants'
import { ARTICLE_SUBMIT, ARTICLE_QUICK_SAVE } from '@/constants/frequency_key.constants'
import { ARTICLE_CHARACTER_LIMIT } from '@/constants/tiptap.constants'
import { type Article } from '@/types/article.types'
import { type ArticleForm } from '@/types/article_form.types'
import type { ResponseData } from '@/types/response_data.types'
import dialog from '@/utils/dialog'
import frequencyLimit from '@/utils/frequency-limit'
import localStorage from '@/utils/local-storage'
import logger from '@/utils/log'
import message from '@/utils/message'
import tiptap from '@/utils/tiptap'
const emit = defineEmits<{
  (event: 'success'): void
}>()
// 弹框显示状态
const isArticleDialogVisible = ref<boolean>(false)
// 编辑状态，判断是创建还是编辑
const isEditingArticle = ref<boolean>(false)
// 添加loading状态
const submitLoading = ref<boolean>(false)
const quickSaveLoading = ref<boolean>(false)
const articleFileInputRef = ref<HTMLInputElement | null>(null)
// 读取文件内容的工具函数
const articleTiptapEditorRef = ref()
const readFileContent = (file: File) => {
  const reader = new FileReader()
  reader.readAsText(file, 'UTF-8')
  reader.onload = (e) => {
    const fileContent = e.target?.result as string
    const lines = fileContent.split('\n')
    logger.debug('import lines: ', lines)
    // 按需获取各部分内容
    const title = (lines[0] ? lines[0].substring(1).trim() : '') || ''
    const tags = lines[1]
      ? lines[1]
          .replace('>', '')
          .split(',')
          .filter(Boolean)
          .map((tag) => tag.trim())
      : []
    const levelScope = lines[9]?.split('|')
    const operationLevel = parseInt(levelScope![0]!.replace('>', '').trim()) || 0
    const publishedScope = parseInt(levelScope![1]!.trim()) || ArticlePublishedScope.PERSONAL
    const contentLines = lines.slice(12)
    const content = contentLines.join('\n')
    const editor = articleTiptapEditorRef.value.editor
    if (!editor) {
      message.error('编辑器尚未准备好')
      return
    }
    try {
      // 假设 Tiptap 配置了 Markdown 扩展
      if (editor.storage.markdown) {
        editor.commands.clearContent(false)
        const prosemirrorJSON = editor.storage.markdown.parser.parse(content)
        editor.commands.setContent(prosemirrorJSON || content, true)
      } else {
        editor.commands.setContent(content, true)
        message.warning('Markdown 格式可能无法完全解析')
      }
      articleForm.value = {
        ...articleForm.value,
        title,
        tags,
        operationLevel,
        publishedScope,
        contentObj: editor.getJSON(), // 更新 contentObj
      }
    } catch (error) {
      logger.error('Error parsing or setting markdown content:', error)
      message.error('解析 Markdown 内容时出错')
      editor.commands.setContent(content, true) // 尝试设置原始内容
      articleForm.value = {
        ...articleForm.value,
        title,
        tags,
        operationLevel,
        publishedScope,
        contentObj: editor.getJSON(), // 更新 contentObj
      }
    }
  }
  reader.onerror = () => {
    message.warning('文件貌似有问题~')
  }
}

const handleArticleFileClick = () => {
  articleFileInputRef.value?.click()
}

const handleArticleFileChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return
  readFileContent(file)
  // 清空 input 的值，确保同一文件可以重复上传
  if (articleFileInputRef.value) {
    articleFileInputRef.value.value = ''
  }
}
// 文章表单数据
const getInitArticleForm = (): ArticleForm => {
  return {
    id: '',
    title: '',
    tags: [],
    operationLevel: 0,
    publishedScope: ArticlePublishedScope.PERSONAL,
    shareUsers: [],
    contentObj: {},
  }
}
const articleForm = ref<ArticleForm>(getInitArticleForm())
const articleFormRef = ref()

// 重置表单
const resetArticleForm = () => {
  articleForm.value = getInitArticleForm()
  shareUserSelectRef.value?.reset()
  // 清空 Tiptap 编辑器内容
  const editor = articleTiptapEditorRef.value?.editor
  if (editor) {
    editor.commands.clearContent(true) // true 表示同时触发更新事件
  }
}

const shareUserSelectRef = ref()

// 生成等级
const generateCommentLevel = computed(() => {
  const optionsArray = []
  for (let i = 0; i <= localStorage.getLoginUser()?.level; i++) {
    optionsArray.push({
      label: 'Lv' + i,
      value: i,
    })
  }
  return optionsArray
})

// 打开创建文章弹框（可由父组件调用，外部可控制是否显示弹框等）
const openCreateArticleDialog = () => {
  isEditingArticle.value = false // 确保是创建模式
  resetArticleForm()
  isArticleDialogVisible.value = true
}

// 打开编辑文章弹框（接收外部传入的文章数据进行编辑，这里假设父组件传入articleData）
const openEditArticleDialog = (articleData: Article) => {
  isEditingArticle.value = true // 编辑模式
  // 转换Article类型到ArticleForm类型
  articleForm.value = {
    id: articleData.id,
    title: articleData.title,
    tags: articleData.tags,
    operationLevel: articleData.operationLevel,
    publishedScope: articleData.publishedScope,
    contentObj: articleData.contentObj,
    shareUsers: articleData.shareUsers || [],
  }
  logger.debug('edit article form: ', articleForm.value)
  isArticleDialogVisible.value = true
}
const handleClose = (): boolean => {
  dialog.warning({
    title: '提示',
    content: '你确定关闭？',
    positiveText: '确定',
    negativeText: '不确定',
    onPositiveClick: () => {
      resetArticleForm()
      isArticleDialogVisible.value = false
    },
    onNegativeClick: () => {},
  })
  return false
}

// 提交表单
const submitArticleForm = () => {
  if (submitLoading.value) {
    return false
  }
  frequencyLimit.debounce(
    ARTICLE_SUBMIT,
    () => {
      saveArticle(true) // 保存并关闭弹窗
    },
    300,
  )
  return false
}

// 快速保存方法（不关闭弹框）
const quickSaveArticleForm = () => {
  if (quickSaveLoading.value) {
    return
  }
  frequencyLimit.debounce(
    ARTICLE_QUICK_SAVE,
    () => {
      saveArticle(false) // 保存但不关闭弹窗
    },
    300,
  )
}

// 通用保存文章方法
const saveArticle = (closeDialogAfterSave = true) => {
  if (!articleFormRef.value?.validate()) {
    return
  }

  // 1. 检查内容是否为空
  const editor = articleTiptapEditorRef.value?.editor
  if (!editor || editor.isEmpty) {
    message.warning('文章内容不能为空哦~')
    return
  }

  // 2. 检查是否正在保存中
  const loadingRef = closeDialogAfterSave ? submitLoading : quickSaveLoading
  if (loadingRef.value) {
    return
  }
  loadingRef.value = true

  // 3. 获取编辑器的最新内容
  const latestContent = editor.getJSON()

  // 构建保存参数
  const params = {
    title: articleForm.value.title,
    tag: articleForm.value.tags.join(','),
    operationLevel: articleForm.value.operationLevel,
    publishedScope: articleForm.value.publishedScope,
    content: tiptap.toJsonString(latestContent), // 使用编辑器的最新内容
    shareUserIds: articleForm.value.shareUsers.map((user) => user.id),
  }

  // 编辑模式需要添加id
  if (isEditingArticle.value && articleForm.value.id) {
    Object.assign(params, { id: articleForm.value.id })
  }

  // 根据模式选择API
  const apiMethod =
    isEditingArticle.value && articleForm.value.id ? articleApi.edit : articleApi.save

  apiMethod(params)
    .then((res: ResponseData) => {
      if (res?.success) {
        // 如果是新建文章，保存成功后立即更新状态
        if (!isEditingArticle.value && res.data) {
          isEditingArticle.value = true
          articleForm.value.id = res.data
          // 强制更新状态，确保下次保存时使用编辑模式
          nextTick(() => {
            logger.debug('Article state updated:', {
              isEditing: isEditingArticle.value,
              articleId: articleForm.value.id,
            })
          })
        }

        if (closeDialogAfterSave) {
          isArticleDialogVisible.value = false
          message.success(isEditingArticle.value ? '修改成功' : '创建成功')
        } else {
          message.success('保存成功')
        }
        emit('success')
      }
    })
    .catch((error) => {
      // 如果是新建文章失败，重置编辑状态
      if (!isEditingArticle.value) {
        isEditingArticle.value = false
        articleForm.value.id = ''
      }
      message.error(error.message || '保存失败')
    })
    .finally(() => {
      loadingRef.value = false
    })
}

// 组件挂载和卸载时添加/移除全局事件监听
onMounted(() => {
  window.addEventListener('keydown', handleGlobalKeyDown)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})

// 全局键盘事件监听
const handleGlobalKeyDown = (e: KeyboardEvent) => {
  if (isArticleDialogVisible.value && e.ctrlKey && e.key === 's') {
    e.preventDefault() // 阻止浏览器默认的保存行为
    e.stopPropagation() // 阻止事件冒泡
    quickSaveArticleForm() // 使用快速保存方法
  }
}

defineExpose({
  openCreateArticleDialog,
  openEditArticleDialog,
})
</script>

<style lang="scss">
.article-modal {
  &.n-modal {
    width: 100vw;
    max-width: 100vw;
  }

  &.n-dialog {
    margin: 0;
  }

  &-content {
    max-width: 100%;
    padding: 0 1.25rem;
    border: var(--border-1);
    border-radius: 0.25rem;
    min-height: 66.5vh;
    min-height: 66.5dvh;
    max-height: 66.5vh;
    max-height: 66.5dvh;
    overflow: hidden;
    box-sizing: border-box;

    .dark-theme & {
      background-color: var(--white-2);
    }

    /* 确保编辑器不超出容器 */
    .tiptap-editor-wrapper {
      max-width: 100%;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      .editor-toolbar {
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .editor-content {
        flex: 1;
        overflow-y: auto;
        max-width: 100%;
        box-sizing: border-box;
      }
    }
  }
}
</style>
