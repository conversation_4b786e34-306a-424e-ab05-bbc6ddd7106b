package com.shenmo.wen.app.core.article.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.article.exception.ArticleException;
import com.shenmo.wen.app.core.article.exception.ArticleExceptionEnum;
import com.shenmo.wen.app.core.article.mapper.WenArticleMapper;
import com.shenmo.wen.app.core.article.mapper.WenArticleShareMapper;
import com.shenmo.wen.app.core.article.pojo.domain.WenHotTag;
import com.shenmo.wen.app.core.article.pojo.entity.WenArticle;
import com.shenmo.wen.app.core.article.pojo.entity.WenArticleShare;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleSaveParam;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleSearchParam;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleUpdateParam;
import com.shenmo.wen.app.core.article.pojo.vo.WenArticleVo;
import com.shenmo.wen.app.core.article.service.WenArticleService;
import com.shenmo.wen.app.core.comment.mapper.WenCommentMapper;
import com.shenmo.wen.app.core.config.properties.ArticleConfigProperties;
import com.shenmo.wen.app.core.favorite.mapper.WenFavoriteMapper;
import com.shenmo.wen.app.core.favorite.pojo.entity.WenFavorite;
import com.shenmo.wen.app.core.interaction.mapper.WenInteractionMapper;
import com.shenmo.wen.app.core.interaction.pojo.entity.WenInteraction;
import com.shenmo.wen.app.core.notification.mapper.WenNotificationMapper;
import com.shenmo.wen.app.core.notification.pojo.entity.WenNotification;
import com.shenmo.wen.app.core.notification.pojo.vo.WenNotificationVo;
import com.shenmo.wen.common.constant.WebSocketMessageConstant;
import com.shenmo.wen.common.enumeration.ArticlePublishedScopeEnum;
import com.shenmo.wen.common.enumeration.InteractionActionEnum;
import com.shenmo.wen.common.enumeration.InteractionTargetEnum;
import com.shenmo.wen.common.enumeration.NotificationReceiveTypeEnum;
import com.shenmo.wen.common.enumeration.NotificationTypeEnum;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.DateUtils;
import com.shenmo.wen.common.util.HttpServletUtils;
import com.shenmo.wen.common.util.IpUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.domain.WenSearchUser;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;

import jakarta.servlet.ServletOutputStream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
public class WenArticleServiceImpl implements WenArticleService {

    private final WenArticleMapper mapper;
    private final WenUserMapper userMapper;
    private final WenInteractionMapper interactionMapper;
    private final WenFavoriteMapper favoriteMapper;
    private final WenNotificationMapper notificationMapper;
    private final SimpMessagingTemplate simpMessagingTemplate;
    private final ArticleConfigProperties articleConfigProperties;
    private final WenCommentMapper commentMapper;
    private final WenArticleShareMapper articleShareMapper;

    @Override
    @Transactional
    public Long save(WenArticleSaveParam param) {
        final long loginId = StpUtil.getLoginIdAsLong();
        // 检查操作等级是否超出用户自身等级
        final WenUser user = userMapper.byId(loginId);
        AssertUtils.isTrue(param.getOperationLevel() <= user.getLevel(), ArticleExceptionEnum.ARTICLE_LEVEL_CONTROL);
        WenArticle article = new WenArticle();
        BeanUtils.copyProperties(param, article);
        article.setUserId(loginId);
        final String ip = IpUtils.getIp();
        article.setIp(ip);
        article.setIpLocation(IpUtils.getIpLocation(ip));
        article.setPublishedAt(System.currentTimeMillis());
        article.setContent(param.getContent().trim());
        mapper.insert(article);

        // 处理分享用户
        if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode() &&
                CollectionUtils.isNotEmpty(param.getShareUserIds())) {
            saveArticleShares(article.getId(), param.getShareUserIds());
        }

        // 发布通知
        publishNotification(user, article, NotificationTypeEnum.PUBLISH_ARTICLE);
        return article.getId();
    }

    /**
     * 保存文章分享用户
     */
    private void saveArticleShares(Long articleId, List<Long> userIds) {
        // 无论userIds是否为空，都先删除原有的分享记录
        articleShareMapper.deleteByArticleId(articleId);

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        // 添加新的分享记录
        List<WenArticleShare> shares = userIds.stream()
                .map(userId -> {
                    WenArticleShare share = new WenArticleShare();
                    share.setId(IdWorker.getId());
                    share.setArticleId(articleId);
                    share.setUserId(userId);
                    return share;
                })
                .collect(Collectors.toList());
        articleShareMapper.batchInsert(shares);
    }

    private void publishNotification(WenUser user, WenArticle article, NotificationTypeEnum notificationType) {
        final WenNotification notification = new WenNotification();
        notification.setUserId(user.getId());
        notification.setArticleId(article.getId());
        notification.setContent(
                String.format("[%s]%s了《%s》", user.getUsername(), notificationType.getText(), article.getTitle()));

        // 设置通知类型
        notification.setType(notificationType.getCode());

        notificationMapper.insert(notification);
        final WenNotificationVo vo = new WenNotificationVo();
        BeanUtils.copyProperties(notification, vo);
        vo.setPublisher(user.getUsername());
        vo.setPublisherAvatar(user.getAvatar());

        // 文章是否公开
        boolean isPublic = article.getPublishedScope() == ArticlePublishedScopeEnum.PUBLIC.getCode();
        // 是否是发布操作
        boolean isPublish = notificationType == NotificationTypeEnum.PUBLISH_ARTICLE;
        // 是否是修改操作
        boolean isModify = notificationType == NotificationTypeEnum.MODIFY_ARTICLE;

        int limit = 10000;
        int offset = 0;
        while (true) {
            List<Long> userIds = userMapper.batchIds(offset, limit);
            if (userIds.isEmpty()) {
                break;
            }

            for (Long nuid : userIds) {
                // 获取用户通知接收类型
                WenUser notifyUser = userMapper.byId(nuid);
                Integer notificationReceiveTypeCode = notifyUser.getNotificationReceiveType();
                // 默认为收藏类型
                if (notificationReceiveTypeCode == null) {
                    notificationReceiveTypeCode = NotificationReceiveTypeEnum.FAVORITE.getCode();
                }

                // 转换为枚举类型
                NotificationReceiveTypeEnum notificationReceiveType = NotificationReceiveTypeEnum
                        .of(notificationReceiveTypeCode);
                // 如果用户关闭了通知，不发送
                if (notificationReceiveType == NotificationReceiveTypeEnum.CLOSE) {
                    continue;
                }

                // 用户是文章作者，始终接收通知（除非设置为关闭）
                boolean isArticleAuthor = Objects.equals(nuid, article.getUserId());

                // 判断用户是否是被分享的用户
                boolean isSharedUser = false;
                if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()) {
                    isSharedUser = articleShareMapper.isSharedToUser(article.getId(), nuid);
                }

                // 根据通知接收类型判断是否发送
                boolean shouldSendNotification = switch (notificationReceiveType) {
                    case CLOSE ->
                        // 关闭 - 不接收任何通知
                        false;
                    case ALL ->
                        // 全部 - 以下情况接收通知：
                        // 1. 自己的文章
                        // 2. 公开的文章
                        // 3. 分享给自己的文章
                        isArticleAuthor || isPublic || isSharedUser;
                    case PUBLISH ->
                        // 发布 - 以下情况接收通知：
                        // 1. 自己的文章发布
                        // 2. 公开的文章发布
                        // 3. 分享给自己的文章发布
                        (isArticleAuthor || isPublic || isSharedUser) && isPublish;
                    case MODIFY ->
                        // 修改 - 以下情况接收通知：
                        // 1. 自己的文章修改
                        // 2. 公开的文章修改
                        // 3. 分享给自己的文章修改
                        (isArticleAuthor || isPublic || isSharedUser) && isModify;
                    case FAVORITE -> {
                        // 收藏 - 只接收收藏的文章的发布，修改通知
                        // 检查用户是否收藏了该文章
                        WenFavorite favorite = favoriteMapper.targetById(nuid, InteractionTargetEnum.ARTICLE.getCode(),
                                article.getId());
                        yield favorite != null && (isPublish || isModify);
                    }
                    case SHARE ->
                        // 分享 - 只接收被分享给自己的文章发布/修改通知
                        isSharedUser && (isPublish || isModify);
                };

                // 发送通知
                if (shouldSendNotification) {
                    // 直接WebSocket通知，不保存未读状态到Redis
                    simpMessagingTemplate.convertAndSendToUser(String.valueOf(nuid),
                            WebSocketMessageConstant.NOTIFICATIONS, vo);
                }
            }
            offset += limit;
        }
    }

    @Override
    @Transactional
    public void edit(WenArticleUpdateParam param) {
        final long loginId = StpUtil.getLoginIdAsLong();
        // 检查操作等级是否超出用户自身等级
        final WenUser user = userMapper.byId(loginId);
        AssertUtils.isTrue(param.getOperationLevel() <= user.getLevel(), ArticleExceptionEnum.ARTICLE_LEVEL_CONTROL);
        final Long articleId = param.getId();
        final WenArticle article = mapper.selectById(articleId);
        // 检查当前用户是否为文章的发布人
        AssertUtils.isTrue(Objects.equals(loginId, article.getUserId()),
                ArticleExceptionEnum.ARTICLE_MODIFY_PERMISSION);
        BeanUtils.copyProperties(param, article);
        final String ip = IpUtils.getIp();
        article.setIp(ip);
        article.setIpLocation(IpUtils.getIpLocation(ip));
        article.setMdTm(System.currentTimeMillis());
        mapper.updateById(article);

        // 处理分享用户
        if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()) {
            // 无论是否为null，都处理分享用户
            saveArticleShares(articleId,
                    param.getShareUserIds() != null ? param.getShareUserIds() : Collections.emptyList());
        } else if (article.getPublishedScope() == ArticlePublishedScopeEnum.PUBLIC.getCode()) {
            // 如果改为公开，则删除所有分享记录
            articleShareMapper.deleteByArticleId(articleId);
        }

        // 发布通知
        publishNotification(user, article, NotificationTypeEnum.MODIFY_ARTICLE);
    }

    @Override
    public List<WenArticleVo> search(WenArticleSearchParam param) {
        final String searchKey = param.getSearchKey();
        final String tag = param.getTag();
        final LambdaQueryWrapper<WenArticle> lambdaQuery = Wrappers.<WenArticle>lambdaQuery()
                .orderByDesc(WenArticle::getCtTm);
        final long loginId = StpUtil.getLoginIdAsLong();

        // 标签筛选和关键字搜索可以共存
        if (StringUtils.isNotBlank(tag)) {
            // 使用FIND_IN_SET函数进行精确匹配
            lambdaQuery.apply("FIND_IN_SET({0}, tag)", tag);
        }

        if (StringUtils.isNotBlank(searchKey)) {
            lambdaQuery.and(l -> {
                // 构建搜索条件
                l.like(WenArticle::getTitle, searchKey)
                        .or()
                        .like(WenArticle::getContent, searchKey);

                // 只有当没有指定tag时，才在tag字段中搜索关键字
                if (StringUtils.isBlank(tag)) {
                    l.or().like(WenArticle::getTag, searchKey);
                }
            });
        }
        // 我的文章
        if (Boolean.TRUE.equals(param.getOwner())) {
            lambdaQuery.eq(WenArticle::getUserId, loginId);
        } else if (Boolean.TRUE.equals(param.getInteraction())) {
            // 互动的文章
            final List<Long> interactionArticleIds = new ArrayList<>(interactionMapper.listArticleIdByUserId(loginId));
            final List<Long> commentArticleIds = commentMapper.listArticleIdsByUserId(loginId);
            interactionArticleIds.addAll(commentArticleIds);
            if (CollectionUtils.isEmpty(interactionArticleIds)) {
                return List.of();
            }
            lambdaQuery.in(CollectionUtils.isNotEmpty(interactionArticleIds), WenArticle::getId, interactionArticleIds);
        } else if (Boolean.TRUE.equals(param.getFavorite())) {
            // 收藏的文章
            final List<Long> interactionArticleIds = favoriteMapper.listArticleIdByUserId(loginId);
            if (CollectionUtils.isEmpty(interactionArticleIds)) {
                return List.of();
            }
            lambdaQuery.in(CollectionUtils.isNotEmpty(interactionArticleIds), WenArticle::getId, interactionArticleIds);
        }
        if (Boolean.FALSE.equals(param.getOwner())) {
            // 查询公开的文章 或 自己的文章 或 分享给自己的文章
            List<Long> sharedArticleIds = articleShareMapper.listArticleIdByUserId(loginId);
            lambdaQuery.and(l -> l.eq(WenArticle::getPublishedScope, ArticlePublishedScopeEnum.PUBLIC.getCode())
                    .or()
                    .eq(WenArticle::getUserId, loginId)
                    .or(CollectionUtils.isNotEmpty(sharedArticleIds),
                            inner -> inner.in(WenArticle::getId, sharedArticleIds)));
        }
        final Long id = param.getId();
        if (Objects.nonNull(id)) {
            lambdaQuery.lt(WenArticle::getId, id);
        }
        lambdaQuery.last("limit " + param.getLoadSize());
        final List<WenArticle> wenArticles = mapper.listBySubContent(lambdaQuery,
                articleConfigProperties.getContentMaxLength());
        return wenArticles.stream().map(article -> {
            final String content = article.getContent();
            article.setContent(mapper.existsByContent(article.getId(), content) ? content
                    : String.format("%s \n%s", content, articleConfigProperties.getCheckDetailView()));
            WenArticleVo vo = entityToVo(article);
            // 添加分享用户信息
            if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()) {
                vo.setShareUsers(getArticleShareUsers(article.getId()));
            }
            return vo;
        }).toList();
    }

    /**
     * 获取文章的分享用户
     */
    private List<WenSearchUser> getArticleShareUsers(Long articleId) {
        List<WenUser> users = articleShareMapper.listUsersByArticleId(articleId);
        return users.stream().map(u -> {
            final WenSearchUser searchUser = new WenSearchUser();
            BeanUtils.copyProperties(u, searchUser);
            return searchUser;
        }).toList();
    }

    @Override
    public String title(Long id) {
        return mapper.titleById(id);
    }

    @Override
    public WenArticleVo detail(Long id) {
        final WenArticle article = mapper.selectById(id);
        AssertUtils.isTrue(Objects.nonNull(article), ArticleExceptionEnum.ARTICLE_NOT_EXISTS);

        // 检查当前用户是否有权限查看该文章
        final long loginId = StpUtil.getLoginIdAsLong();
        // 如果文章是个人可见，检查是否是文章作者或被分享用户
        if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()) {
            boolean isOwner = Objects.equals(loginId, article.getUserId());
            boolean isSharedUser = articleShareMapper.isSharedToUser(article.getId(), loginId);
            AssertUtils.isTrue(isOwner || isSharedUser, ArticleExceptionEnum.ARTICLE_LOOK_PERMISSION);
        }

        final WenArticleVo articleVo = entityToVo(article);
        final int targetCode = InteractionTargetEnum.ARTICLE.getCode();
        final WenInteraction interaction = interactionMapper.targetById(loginId, targetCode, id);
        if (Objects.nonNull(interaction)) {
            switch (InteractionActionEnum.of(interaction.getActionType())) {
                case LIKE -> articleVo.setIsLike(true);
                case DISLIKE -> articleVo.setIsDislike(true);
            }
        }
        final WenFavorite favorite = favoriteMapper.targetById(loginId, targetCode, id);
        if (Objects.nonNull(favorite)) {
            articleVo.setIsFavorite(true);
        }

        // 添加分享用户信息
        if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()) {
            articleVo.setShareUsers(getArticleShareUsers(article.getId()));
        }

        return articleVo;
    }

    private WenArticleVo entityToVo(WenArticle article) {
        final WenArticleVo articleVo = new WenArticleVo();
        BeanUtils.copyProperties(article, articleVo);
        final Long articleUserId = article.getUserId();
        WenUser user = userMapper.byId(articleUserId);
        articleVo.setPublisher(user.getUsername());
        articleVo.setPublisherAvatar(user.getAvatar());
        articleVo.setIsOwner(Objects.equals(StpUtil.getLoginIdAsLong(), articleUserId));
        articleVo.setLastModified(article.getMdTm());
        return articleVo;
    }

    private String buildFileContent(WenArticleVo vo) {
        final String title = vo.getTitle();
        final String tag = vo.getTag();
        final Long publishedAt = vo.getPublishedAt();
        final String ipLocation = Optional.ofNullable(vo.getIpLocation()).orElse("");
        final String publisher = vo.getPublisher();
        final Integer operationLevel = vo.getOperationLevel();
        final String content = vo.getContent();
        return String.format("""
                # %s
                > %s
                >
                > %s
                >
                > %s
                >
                > %s
                >
                > %s
                \s
                \s
                %s""", title, tag, DateUtils.dateFormat(new Date(publishedAt)), ipLocation, publisher,
                operationLevel + " | " + vo.getPublishedScope(), content);
    }

    @Override
    public void download(Long id) throws IOException {
        final WenArticle article = mapper.selectById(id);
        final long loginId = StpUtil.getLoginIdAsLong();
        // 检查操作等级是否超出用户自身等级
        final WenUser user = userMapper.byId(loginId);
        AssertUtils.isTrue(article.getOperationLevel() <= user.getLevel(), ArticleExceptionEnum.ARTICLE_LEVEL_CONTROL);
        final WenArticleVo vo = entityToVo(article);
        final String fileContent = buildFileContent(vo);
        try (ServletOutputStream servletOutputStream = HttpServletUtils.buildFileOutputStream(article.getTitle())) {
            servletOutputStream.write(fileContent.getBytes(StandardCharsets.UTF_8));
            servletOutputStream.flush();
        }
    }

    @Override
    public void togglePublishedScope(Long id) {
        // 获取当前登录用户ID
        Long currentUserId = StpUtil.getLoginIdAsLong();
        // 查询文章
        WenArticle article = mapper.selectById(id);
        AssertUtils.isNotNull(article, new ArticleException(ArticleExceptionEnum.ARTICLE_NOT_EXISTS));
        // 验证是否是文章作者
        AssertUtils.isTrue(Objects.equals(article.getUserId(), currentUserId),
                ArticleExceptionEnum.ARTICLE_MODIFY_PERMISSION);
        // 切换发布范围：PUBLIC(0)-公开，PERSONAL(1)-个人
        int newScope = article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()
                ? ArticlePublishedScopeEnum.PUBLIC.getCode()
                : ArticlePublishedScopeEnum.PERSONAL.getCode();
        article.setPublishedScope(newScope);

        // 如果切换为公开，则删除所有分享记录
        if (newScope == ArticlePublishedScopeEnum.PUBLIC.getCode()) {
            articleShareMapper.deleteByArticleId(id);
        }
        // 如果切换为个人，暂时不添加分享记录，等用户手动添加

        // 更新文章
        mapper.updateById(article);
    }

    @Override
    public List<WenHotTag> getHotTags(int limit) {
        // 获取当前登录用户ID
        Long loginId = StpUtil.getLoginIdAsLong();

        // 查询所有公开的文章或用户自己的文章或分享给用户的文章
        List<Long> sharedArticleIds = articleShareMapper.listArticleIdByUserId(loginId);
        LambdaQueryWrapper<WenArticle> lambdaQuery = Wrappers.<WenArticle>lambdaQuery()
                .select(WenArticle::getTag)
                .and(l -> l.eq(WenArticle::getPublishedScope, ArticlePublishedScopeEnum.PUBLIC.getCode())
                        .or()
                        .eq(WenArticle::getUserId, loginId)
                        .or(CollectionUtils.isNotEmpty(sharedArticleIds),
                                inner -> inner.in(WenArticle::getId, sharedArticleIds)));

        List<WenArticle> articles = mapper.selectList(lambdaQuery);

        // 统计标签出现次数 - 使用不区分大小写的Map
        Map<String, Integer> tagCountMap = new HashMap<>();

        for (WenArticle article : articles) {
            String tagStr = article.getTag();
            if (StringUtils.isNotBlank(tagStr)) {
                String[] tags = tagStr.split(",");
                for (String tag : tags) {
                    String trimmedTag = tag.trim();
                    if (StringUtils.isNotBlank(trimmedTag)) {
                        // 转换为小写进行统计，不区分大小写
                        String lowerCaseTag = trimmedTag.toLowerCase();
                        // 更新计数
                        tagCountMap.put(lowerCaseTag, tagCountMap.getOrDefault(lowerCaseTag, 0) + 1);
                    }
                }
            }
        }

        // 按出现次数排序并限制返回数量，转换为WenHotTag对象列表
        return tagCountMap.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(limit)
                .map(entry -> WenHotTag.builder()
                        .name(entry.getKey())
                        .count(entry.getValue())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void delete(Long id) {
        // 获取当前登录用户ID
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 查询文章
        WenArticle article = mapper.selectById(id);
        AssertUtils.isNotNull(article, new ArticleException(ArticleExceptionEnum.ARTICLE_NOT_EXISTS));
        
        // 验证是否是文章作者
        AssertUtils.isTrue(Objects.equals(article.getUserId(), currentUserId),
                ArticleExceptionEnum.ARTICLE_MODIFY_PERMISSION);

        // 删除文章相关的所有数据
        // 1. 删除文章分享记录
        articleShareMapper.deleteByArticleId(id);
        
        // 2. 删除文章评论
        commentMapper.deleteByArticleId(id);
        
        // 3. 删除文章互动记录（点赞、点踩）
        interactionMapper.deleteByTargetId(InteractionTargetEnum.ARTICLE.getCode(), id);
        
        // 4. 删除文章收藏记录
        favoriteMapper.deleteByTargetId(InteractionTargetEnum.ARTICLE.getCode(), id);
        
        // 5. 删除文章相关的通知
        notificationMapper.deleteByArticleId(id);
        
        // 6. 最后删除文章本身
        mapper.deleteById(id);
    }
}
