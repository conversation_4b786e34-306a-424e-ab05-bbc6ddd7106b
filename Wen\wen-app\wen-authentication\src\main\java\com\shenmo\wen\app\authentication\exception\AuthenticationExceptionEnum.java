package com.shenmo.wen.app.authentication.exception;

import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExceptionType;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ExceptionType(type = AuthenticationException.class, module = AuthenticationExceptionEnum.MODULE)
public enum AuthenticationExceptionEnum implements ExceptionEnum {

    /**
     * 手机号或密码错误
     */
    PHONE_OR_PASSWORD_MISTAKE(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "不对不对，手机号跟密码不对")),

    /**
     * 手机号已存在
     */
    PHONE_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "这个手机号已经有了")),

    /**
     * 用户名已存在
     */
    USERNAME_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "换个用户名吧，别人用了"));

    public static final String MODULE = "002";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    AuthenticationExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
