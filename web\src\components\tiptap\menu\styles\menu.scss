// 气泡菜单样式
.editor-bubble-menu {
  background-color: white;
  border: 1px solid gray;
  border-radius: 0.5rem;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 5%),
    0 2px 8px rgba(0, 0, 0, 10%);
  display: flex;
  flex-wrap: wrap;
  z-index: 100;
  max-width: min(16rem, 100%);
}

// 浮动菜单样式
.editor-floating-menu {
  background-color: white;
  border: 1px solid gray;
  border-radius: 0.5rem;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 5%),
    0 2px 8px rgba(0, 0, 0, 10%);
  display: flex;
  z-index: 100;
}

// 工具栏样式
.tiptap-editor-wrapper {
  .editor-toolbar {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 4px;
    padding: 4px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
  }

  .editor-toolbar-bgc {
    background-color: white;
  }

  /* 全屏模式下工具栏背景色 */
  &.tiptap-fullscreen .editor-toolbar-bgc {
    background-color: inherit !important;
  }
}

/* 为暗色模式添加编辑器气泡菜单样式 */
.dark-theme .editor-bubble-menu,
.dark-theme .editor-floating-menu {
  background-color: var(--white-2);
  border: 1px solid var(--gray-3);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 5%),
    0 2px 8px rgba(0, 0, 0, 30%);
}

/* 为暗色主题的工具栏添加适当的背景颜色 */
.dark-theme .tiptap-editor-wrapper .editor-toolbar-bgc {
  background-color: var(--white-2);
}

/* 确保暗模式下全屏工具栏的背景色 */
.dark-theme .tiptap-editor-wrapper.tiptap-fullscreen .editor-toolbar-bgc {
  background-color: var(--white-2) !important;
}
