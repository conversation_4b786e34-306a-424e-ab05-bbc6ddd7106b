<template>
  <div class="article-layout">
    <!-- 左侧文章 -->
    <!-- 骨架屏 -->
    <ArticleSkeleton :show="articleLoading" />
    <div v-if="!articleLoading" class="article-info-container">
      <div class="article-header">
        <div class="article-header-content-wrapper">
          <div class="article-header-content">
            <h2>{{ article.title }}</h2>
            <div class="article-tag-container">
              <NTag class="article-tag" v-for="tag in article.tags" :key="tag" type="primary">{{
                tag
              }}</NTag>
            </div>
          </div>
        </div>
        <div class="flex-column-start">
          <NGradientText
            type="info"
            class="display-block time-clickable"
            @click="toggleTimeFormat('publish')"
          >
            发布时间：{{
              article.showExactPublishTime ? article.exactPublishedAt : article.publishedAt
            }}
          </NGradientText>
          <NGradientText
            type="info"
            class="display-block time-clickable"
            @click="toggleTimeFormat('modify')"
          >
            最近修改：{{
              article.showExactModifyTime ? article.exactLastModified : article.lastModified
            }}
          </NGradientText>
          <NGradientText type="info" class="display-block">
            拥有者：{{ article.publisher }} | 等级：{{ article.operationLevel }} | ip:
            {{ article.ipLocation }}
          </NGradientText>
        </div>
        <div class="action-buttons-container">
          <div class="edit-button-container">
            <NIcon
              class="cursor-pointer"
              size="28"
              v-if="article.isOwner"
              @click="openEditArticleDialog"
              ><DocumentEdit16Regular
            /></NIcon>
            <NIcon class="cursor-pointer" size="28" @click="backHome"><RollbackOutlined /></NIcon>
          </div>
          <!-- 互动按钮 -->
          <div class="interaction-container">
            <NIcon
              :color="article.isLike ? 'var(--blue)' : ''"
              size="16"
              class="cursor-pointer"
              @click="interactionBtn(article.id, 1)"
            >
              <LikeOutlined />
            </NIcon>
            {{ article.likeCount }}
            <NIcon
              :color="article.isDislike ? 'var(--blue)' : ''"
              size="16"
              class="cursor-pointer"
              @click="interactionBtn(article.id, 0)"
            >
              <DislikeOutlined />
            </NIcon>
            {{ article.dislikeCount }}
            <NIcon
              :color="article.isFavorite ? 'var(--blue)' : ''"
              size="18"
              class="cursor-pointer"
              @click="favoriteBtn(article.id)"
              ><Star48Regular
            /></NIcon>
            {{ article.favoriteCount }}
          </div>
          <div class="comment-count-container">
            <NIcon size="20"><CommentOutlined /></NIcon>{{ article.commentCount }}
          </div>
        </div>
      </div>
      <div class="article-content">
        <NScrollbar>
          <div style="padding-right: 1rem">
            <TiptapEditor
              v-model="article.contentObj"
              :editable="false"
              :file-bucket="ARTICLE"
              :all-extensions="true"
              :character-limit="ARTICLE_CHARACTER_LIMIT"
              :use-thumbnail="true"
            />
          </div>
        </NScrollbar>
      </div>
    </div>
    <!-- 编辑文章弹框 -->
    <ArticleModal ref="articleModalRef" @success="handleArticleSubmitSuccess" />
    <!-- 右侧评论 -->
    <CommentInfo
      ref="commentInfoRef"
      :articleId="getArticleId"
      @quick-reply-end="loadArticleDetailCount"
      @send-end="loadArticleDetailCount"
    />
  </div>
</template>
<script lang="tsx" setup>
import { RollbackOutlined, LikeOutlined, DislikeOutlined, CommentOutlined } from '@vicons/antd'
import { DocumentEdit16Regular, Star48Regular } from '@vicons/fluent'
import { NIcon, NTag, NGradientText, NSkeleton, NScrollbar } from 'naive-ui'
import { ref, onMounted, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'

import articleApi from '@/api/article'
import favoriteApi from '@/api/favorite'
import interactionApi from '@/api/interaction'
import ArticleModal from '@/components/ArticleModal.vue'
import ArticleSkeleton from '@/components/ArticleSkeleton.vue'
import CommentInfo from '@/components/CommentInfo.vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { ARTICLE } from '@/constants/bucket.constants'
import { ARTICLE_CHARACTER_LIMIT } from '@/constants/tiptap.constants'
import router from '@/router'
import { useArticleStore } from '@/stores/index'
import { type Article } from '@/types/article.types'
import type { ResponseData } from '@/types/response_data.types'
import dateTime from '@/utils/date-time'
import logger from '@/utils/log'
import message from '@/utils/message'
import { activeTheme } from '@/utils/theme'
import tiptap from '@/utils/tiptap'
const articleStore = useArticleStore()
const getArticleId = () => {
  return articleStore.getId
}

// 切换时间显示格式
const toggleTimeFormat = (type: 'publish' | 'modify') => {
  if (type === 'publish') {
    if (article.value.showExactPublishTime === undefined) {
      article.value.showExactPublishTime = true
    } else {
      article.value.showExactPublishTime = !article.value.showExactPublishTime
    }
  } else {
    if (article.value.showExactModifyTime === undefined) {
      article.value.showExactModifyTime = true
    } else {
      article.value.showExactModifyTime = !article.value.showExactModifyTime
    }
  }
}

// 文章数据
const article = ref<Article>({} as Article)
onMounted(() => {
  loadArticleDetail()
})
const route = useRoute()
watch(route, (newRoute, oldRoute) => {
  // 只在articleId变化时重新加载文章内容
  if (newRoute.params.articleId !== oldRoute?.params?.articleId) {
    loadArticleDetail()
  }
})

const articleLoading = ref(true)
const loadArticleDetail = () => {
  articleLoading.value = true
  const articleId = getArticleId()
  if (articleId) {
    articleApi
      .detail(articleId)
      .then((res: ResponseData) => {
        const detail = res?.data
        if (detail) {
          article.value = {
            ...detail,
            contentObj: tiptap.toJsonObject(detail.content),
            tags: detail.tag.split(',') as string[],
            isOwner: detail.isOwner === true,
            publishedAt: dateTime.getRelativeTime(detail.publishedAt),
            lastModified: dateTime.getRelativeTime(detail.lastModified),
            exactPublishedAt: dateTime.toTimeString(detail.publishedAt),
            exactLastModified: dateTime.toTimeString(detail.lastModified),
          }
          articleLoading.value = false
        }
        logger.debug('article detail: ', article.value)
      })
      .catch((error) => {
        articleLoading.value = false
        // 处理权限错误或不存在的文章
        if (error.response && error.response.status === 403) {
          message.error('哎呀，您没有权限查看这篇文章')
          router.push('/')
        } else {
          message.error('加载文章失败，请稍后重试')
        }
      })
  }
}

const articleModalRef = ref()
const commentInfoRef = ref()
// 打开编辑文章弹框
const openEditArticleDialog = () => {
  articleModalRef.value.openEditArticleDialog(article.value)
}
// 处理文章提交成功的回调函数（根据弹框组件触发的事件来执行相应逻辑）
const handleArticleSubmitSuccess = () => {
  loadArticleDetail()
  commentInfoRef.value.loadCurrentCommentList()
}
// 返回首页
const backHome = () => {
  router.push('/')
}

// ------------------------互动------------------------------------------------------------------------------------------------
const interactionBtn = (articleId: string, actionType: number) => {
  const reqParam = {
    targetType: 1,
    targetId: articleId,
    actionType: actionType,
  }
  interactionApi.save(reqParam).then((res: ResponseData) => {
    const data = res?.data
    if (data) {
      article.value.likeCount = data.likeCount
      article.value.dislikeCount = data.dislikeCount
      const like = actionType === 1
      // 取消互动
      if (data.cancel) {
        if (like) {
          message.info('赞取消')
          article.value.isLike = false
        } else {
          message.info('踩取消')
          article.value.isDislike = false
        }
      } else {
        if (like) {
          message.success('赞 :)')
          article.value.isLike = true
        } else {
          message.warning('踩 :(')
          article.value.isDislike = true
        }
      }
    }
  })
}
// ------------------------收藏------------------------------------------------------------------------------------------------
const favoriteBtn = (articleId: string) => {
  const reqParam = {
    targetType: 1,
    targetId: articleId,
  }
  favoriteApi.save(reqParam).then((res: ResponseData) => {
    const data = res?.data
    if (data) {
      article.value.favoriteCount = data.count
      if (data.cancel) {
        message.info('取消收藏')
        article.value.isFavorite = false
      } else {
        message.success('已收藏')
        article.value.isFavorite = true
      }
    }
  })
}

const loadArticleDetailCount = () => {
  const articleId = getArticleId()
  if (articleId) {
    articleApi.detail(articleId).then((res: ResponseData) => {
      const detail: Article = res?.data
      if (detail) {
        // 标识是否为所有者的字段
        article.value.likeCount = detail.likeCount
        article.value.dislikeCount = detail.dislikeCount
        article.value.favoriteCount = detail.favoriteCount
        article.value.commentCount = detail.commentCount
      }
    })
  }
}

// 监听主题变化并优化文章编辑器刷新
watch(activeTheme, () => {
  // 等待DOM更新
  nextTick(() => {
    if (!articleLoading.value) {
      // 刷新文章内容编辑器
      const articleEditor = document.querySelector('.article-content .ProseMirror')
      if (articleEditor instanceof HTMLElement) {
        // 使用轻量级方式触发编辑器重绘
        articleEditor.classList.add('theme-priority')
        void articleEditor.offsetHeight
      }
    }
  })
})
</script>

<style scoped lang="scss">
.article-layout {
  display: flex;
  height: 100vh;
  height: 100dvh;
  width: 100vw;
  width: 100dvw;
  background-color: var(--gray-3);
  overflow-x: hidden;

  :deep(.article-info-container) {
    flex: 0 0 65vw;
    flex: 0 0 65dvw;
    width: 65%;
    padding: 1.25rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100vh;
    height: 100dvh;

    .article-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      position: relative;
      margin: 1.25rem 0;

      .article-header-content-wrapper {
        margin-top: 3rem;

        .article-header-content {
          display: flex;
          flex-direction: column;
          text-align: center;

          .article-tag-container {
            display: flex;
            gap: 0.5rem;
            justify-content: center;

            .article-tag {
              margin-left: 0;
            }
          }
        }
      }
    }

    .time-clickable {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        opacity: 0.8;
        text-decoration: underline;
      }
    }

    .action-buttons-container {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      position: absolute;
      top: -5%;
      right: 1%;

      .edit-button-container {
        margin-bottom: 0.5rem;
        display: flex;
        gap: 0.25rem;
      }

      .interaction-container {
        display: flex;
        font-size: 0.8rem;
        gap: 0.4rem;
        align-items: center;
      }

      .comment-count-container {
        margin-top: 0.5rem;
        margin-right: 0.25rem;
        font-size: 0.8rem;
        display: flex;
      }
    }

    .article-content {
      padding: 1rem 0 1rem 1rem;
      border-radius: 0.5rem;
      width: 90%;
      overflow-y: auto;
      background-color: var(--white-1);

      // 确保图片在文章内容中比例正确
      :deep(.resizable-image-wrapper),
      :deep(img) {
        max-width: 100%;
        height: auto;
        object-fit: contain;
      }
    }
  }
}

@media (width <= 55rem) {
  .article-layout {
    flex-direction: column;
  }

  .article-layout .article-info-container,
  .article-layout .comment-info-container {
    flex: 0 0 100vw;
    flex: 0 0 100dvw;
    width: 100%;
  }

  .article-layout .article-info-container {
    .article-content {
      width: 95%;
      padding: 1rem 0 1rem 1rem;

      // 小屏幕上进一步优化图片显示
      :deep(.ProseMirror) {
        p > .resizable-image-wrapper,
        p > img {
          max-width: 100% !important;
          min-width: unset !important;
          width: auto !important;
          height: auto !important;
        }
      }
    }

    .article-header {
      margin: 0.75rem 0;

      h2 {
        font-size: 1.4rem;
        word-break: break-word;
      }
    }
  }
}
</style>
