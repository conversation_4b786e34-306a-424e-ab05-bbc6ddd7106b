// Vue 版本的图片节点视图样式
// 这些样式专门用于 ImageNodeView.vue 组件

.resizable-image-wrapper {
  position: relative;
  display: inline-block;
  max-width: 100%;
  box-sizing: border-box;
  // 确保包装器尺寸与图片完全一致
  line-height: 0;
  font-size: 0;

  &.resizing {
    user-select: none;

    img {
      pointer-events: none;
    }
  }

  // 确保图片完全填充包装器
  img {
    display: block;
    width: 100%;
    height: auto;
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
  }

  // 调整大小控制点样式
  .resize-handle {
    position: absolute;
    background: #2d8cf0;
    border: 2px solid white;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    cursor: pointer;
    z-index: 100;
    opacity: 0;
    transition: opacity 0.2s ease;
    // 确保控制点居中对齐
    box-sizing: border-box;

    // 角控制点 - 精确定位到图片边角
    &.handle-top-left {
      top: -6px;
      left: -6px;
      cursor: nw-resize;
    }

    &.handle-top-right {
      top: -6px;
      right: -6px;
      cursor: ne-resize;
    }

    &.handle-bottom-left {
      bottom: -6px;
      left: -6px;
      cursor: sw-resize;
    }

    &.handle-bottom-right {
      bottom: -6px;
      right: -6px;
      cursor: se-resize;
    }

    // 边控制点 - 使用 transform 确保精确居中
    &.handle-top {
      top: -6px;
      left: 50%;
      transform: translateX(-50%);
      cursor: n-resize;
    }

    &.handle-right {
      right: -6px;
      top: 50%;
      transform: translateY(-50%);
      cursor: e-resize;
    }

    &.handle-bottom {
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);
      cursor: s-resize;
    }

    &.handle-left {
      left: -6px;
      top: 50%;
      transform: translateY(-50%);
      cursor: w-resize;
    }

    // 移动设备优化
    @media (width <= 768px) {
      width: 16px;
      height: 16px;
      border-width: 3px;

      &.handle-top-left,
      &.handle-top-right,
      &.handle-bottom-left,
      &.handle-bottom-right {
        inset: -8px;
      }

      &.handle-top,
      &.handle-bottom {
        top: -8px;
        bottom: -8px;
      }

      &.handle-left,
      &.handle-right {
        left: -8px;
        right: -8px;
      }
    }
  }

  // 尺寸信息显示
  .resize-info {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 80%);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 101;
    pointer-events: none;
  }
}
