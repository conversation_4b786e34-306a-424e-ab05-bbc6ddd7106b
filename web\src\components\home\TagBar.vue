<template>
  <div class="tag-bar-container" v-if="hotTags.length > 0">
    <NTag
      v-for="tag in hotTags"
      :key="tag.name"
      :type="selectedTag === tag.name ? 'primary' : 'default'"
      class="hot-tag"
      :bordered="false"
      @click="handleTagClick(tag.name)"
    >
      {{ tag.name }} ({{ tag.count }})
    </NTag>
  </div>
</template>

<script lang="ts" setup>
import { NTag } from 'naive-ui'
import { ref, onMounted, watch } from 'vue'

import articleApi from '@/api/article'
import type { ResponseData } from '@/types/response_data.types'
import type { HotTag } from '@/types/search.types'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'tagSelected'])

// 热门标签
const hotTags = ref<HotTag[]>([])
const selectedTag = ref(props.modelValue)

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    selectedTag.value = newVal
  },
)

// 加载热门标签
const loadHotTags = () => {
  articleApi.getHotTags(10).then((res: ResponseData) => {
    if (res.data) {
      // 直接使用返回的标签统计对象数组
      hotTags.value = res.data
    }
  })
}

// 处理标签点击
const handleTagClick = (tagName: string) => {
  // 如果点击当前已选中的标签，则取消选择
  if (selectedTag.value === tagName) {
    selectedTag.value = ''
  } else {
    selectedTag.value = tagName
  }

  // 更新外部值并触发事件
  emit('update:modelValue', selectedTag.value)
  emit('tagSelected', selectedTag.value)
}

// 组件挂载时加载热门标签
onMounted(() => {
  loadHotTags()
})
</script>

<style scoped lang="scss">
.tag-bar-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.25rem 1.25rem 0.5rem 1.25rem;
  background-color: var(--creamy-white-2);
  justify-content: center;

  .hot-tag {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}
</style>
