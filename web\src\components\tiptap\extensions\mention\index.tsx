import Mention from '@tiptap/extension-mention'
import { VueRenderer } from '@tiptap/vue-3'
import tippy from 'tippy.js'

import fileApi from '@/api/file'
import userApi from '@/api/user'
import MentionList from '@/components/tiptap/extensions/mention/MentionList.vue'
import type { ResponseData } from '@/types/response_data.types'

// 定义 @mention 节点的属性类型
export interface MentionAttributes {
  id: string
  label: string
  avatar?: string
}

const mention = Mention.extend({
  name: 'mention',

  addAttributes() {
    return {
      ...this.parent?.(),
      avatar: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-avatar'),
        renderHTML: (attributes: MentionAttributes) => {
          return attributes.avatar ? { 'data-avatar': attributes.avatar } : {}
        },
      },
    }
  },

  renderHTML({ node, HTMLAttributes }) {
    const attrs = node.attrs as MentionAttributes
    const children: Array<[string, Record<string, string>, string]> = [
      ['span', { class: 'mention-name' }, `@${attrs.label}`],
    ]

    if (attrs.avatar) {
      const avatarUrl = fileApi.getResourceURL(attrs.avatar)
      children.push([
        'img',
        {
          class: 'mention-avatar',
          src: avatarUrl,
          alt: attrs.label,
          loading: 'lazy',
        },
        '',
      ])
    }

    return [
      'span',
      {
        ...HTMLAttributes,
        'data-type': 'mention',
        'data-id': attrs.id,
        'data-label': attrs.label,
        'data-avatar': attrs.avatar || '',
        class: 'mention',
        contenteditable: 'false',
      },
      ...children,
    ]
  },
})

const mentionExtension = mention.configure({
  suggestion: {
    items: async ({ query }) => {
      if (!query) return []
      const res: ResponseData = await userApi.searchUser(query)
      return res.data
    },

    render: () => {
      let component: VueRenderer
      let popup: ReturnType<typeof tippy>

      return {
        onStart: (props) => {
          component = new VueRenderer(MentionList, {
            props,
            editor: props.editor,
          })

          if (!props.clientRect) {
            return
          }
          popup = tippy('body', {
            getReferenceClientRect: () => {
              const rect = props.clientRect?.()
              return rect instanceof DOMRect ? rect : new DOMRect()
            },
            appendTo: () => document.body,
            content: component.element!,
            showOnCreate: true,
            interactive: true,
            trigger: 'manual',
            placement: 'bottom-start',
          })
        },

        onUpdate(props) {
          component.updateProps(props)

          if (!props.clientRect) {
            return
          }

          popup[0].setProps({
            getReferenceClientRect: () => {
              const rect = props.clientRect?.()
              return rect instanceof DOMRect ? rect : new DOMRect()
            },
          })
        },

        onKeyDown(props) {
          if (props.event.key === 'Escape') {
            popup[0].hide()
            return true
          }

          return component.ref?.onKeyDown(props)
        },

        onExit() {
          popup[0].destroy()
          component.destroy()
        },
      }
    },
  },
})

export default mentionExtension
