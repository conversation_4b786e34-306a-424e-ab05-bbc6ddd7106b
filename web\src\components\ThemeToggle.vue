<template>
  <div class="theme-toggle-scene" :class="{ 'is-dark': isDarkTheme }" @click="handleThemeSwitch">
    <div class="sky">
      <div class="sun" :class="{ 'sun-set': isDarkTheme }">
        <SunIcon />
      </div>
      <div class="moon" :class="{ 'moon-rise': isDarkTheme }">
        <MoonIcon />
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { computed } from 'vue'
import { activeTheme, toggleTheme, ThemeType } from '@/utils/theme'

// 主题相关
const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

// 处理主题切换
const handleThemeSwitch = () => {
  toggleTheme()
}

// 自定义内联SVG图标组件
const SunIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100%" height="100%">
    <circle cx="12" cy="12" r="5" fill="currentColor" />
    <path
      fill="none"
      d="M12,3V5M12,19V21M21,12H19M5,12H3M18.364,5.636L16.95,7.05M7.05,16.95L5.636,18.364M18.364,18.364L16.95,16.95M7.05,7.05L5.636,5.636"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
    />
  </svg>
)

const MoonIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
    <path
      fill="currentColor"
      d="M283.211 512c78.962 0 151.079-35.925 198.857-94.792 7.068-8.708-.639-21.43-11.562-19.35-124.203 23.654-238.262-71.576-238.262-196.954 0-72.222 38.662-138.635 101.498-174.394 9.686-5.512 7.25-20.197-3.756-22.23A258.156 258.156 0 0 0 283.211 0c-141.309 0-256 114.511-256 256 0 141.309 114.511 256 256 256z"
    />
  </svg>
)
</script>

<style scoped>
/* 日月交替场景 - 快速版 */
.theme-toggle-scene {
  position: relative;
  width: 48px;
  height: 28px;
  border-radius: 14px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 20%);
  background: linear-gradient(180deg, #4a90e2 0%, #a5d6ff 100%);
}

.theme-toggle-scene.is-dark {
  background: linear-gradient(180deg, #000814 0%, #001440 100%);
}

.sky {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.sun,
.moon {
  position: absolute;
  width: 18px;
  height: 18px;
  transition: all 0.25s ease-out;
}

.sun {
  color: #ffd700;
  left: 14px;
  top: 5px;
  opacity: 1;
  z-index: 2;
}

.sun-set {
  transform: translateY(30px);
  opacity: 0;
}

.moon {
  color: #fff;
  right: 12px;
  top: 4px;
  opacity: 0;
  z-index: 2;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 80%));
  width: 20px;
  height: 20px;
}

.moon-rise {
  opacity: 1;
}

/* 简化云彩和星星装饰元素 */
.theme-toggle-scene::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 4px;
  background-color: rgba(255, 255, 255, 70%);
  border-radius: 4px;
  left: 5px;
  top: 18px;
  transition: all 0.25s ease;
  opacity: 1;
  z-index: 1;
}

.theme-toggle-scene::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 3px;
  background-color: rgba(255, 255, 255, 50%);
  border-radius: 3px;
  right: 7px;
  top: 6px;
  transition: all 0.25s ease;
  opacity: 1;
  z-index: 1;
}

.theme-toggle-scene.is-dark::before,
.theme-toggle-scene.is-dark::after {
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 90%);
}

.theme-toggle-scene.is-dark::before {
  left: 12px;
  top: 8px;
}

.theme-toggle-scene.is-dark::after {
  right: 10px;
  top: 16px;
}

/* 简化星星效果 */
.theme-toggle-scene.is-dark .sky::before,
.theme-toggle-scene.is-dark .sky::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  background-color: white;
  border-radius: 50%;
  opacity: 0.9;
  z-index: 1;
}

.theme-toggle-scene.is-dark .sky::before {
  left: 20px;
  top: 10px;
}

.theme-toggle-scene.is-dark .sky::after {
  left: 32px;
  top: 6px;
}

/* 鼠标悬停效果 */
.theme-toggle-scene:hover {
  transform: scale(1.02);
}

/* 点击效果 */
.theme-toggle-scene:active {
  transform: scale(0.98);
}
</style>
