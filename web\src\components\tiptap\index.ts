// 导出主组件
import { useEditor, createEditor } from './core/editor/EditorCore'
import TipTapEditor from './core/TipTapEditor.vue'
// 导出可复用逻辑
import { useEditorEvents, createEditorEvents } from './events/EditorEventManager'
// 导出组合式函数
export * from './composables'
import Bilibili from './extensions/bilibili'
import CodeBlockToolbar from './extensions/code-block/CodeBlockToolbar.vue'
import ColorPicker from './extensions/color/ColorPicker.vue'
import FormatPainterBtn from './extensions/format-painter/FormatPainterBtn.vue'
import { FormatPainterExtension } from './extensions/format-painter/FormatPainterExtension'
import Fullscreen, { useFullscreen } from './extensions/fullscreen'
import { createImageExtension, handleImageUpload, useImageUpload } from './extensions/image'
import Mention from './extensions/mention'
import TaskItem from './extensions/task-item'
import EditorBubbleMenu from './menu/EditorBubbleMenu.vue'
import EditorFloatingMenu from './menu/EditorFloatingMenu.vue'
import { useModal, createEditorModal } from './modal/EditorModal'
import EditorModalHandler from './modal/EditorModalHandler.vue'
import ToolbarButtonGroup from './toolbar/components/ToolbarButtonGroup.vue'
import { allButtonConfigs } from './toolbar/configs/toolbarButtons'
import EditorToolbar from './toolbar/EditorToolbar.vue'
import TiptapBtn from './toolbar/TiptapBtn.vue'

export {
  // 主组件
  TipTapEditor,

  // 工具栏组件
  EditorToolbar,
  TiptapBtn,
  ToolbarButtonGroup,

  // 菜单组件
  EditorBubbleMenu,
  EditorFloatingMenu,

  // 模态框组件
  EditorModalHandler,

  // 可复用逻辑
  useEditor,
  useEditorEvents,
  useModal,
  useFullscreen,

  // 新的创建函数
  createEditor,
  createEditorEvents,
  createEditorModal,

  // 扩展组件
  ColorPicker,
  FormatPainterBtn,
  CodeBlockToolbar,

  // 扩展
  createImageExtension,
  handleImageUpload,
  useImageUpload,
  FormatPainterExtension,
  Bilibili,
  Mention,
  TaskItem,
  Fullscreen,

  // 配置
  allButtonConfigs,
}

// 默认导出主编辑器组件
export default TipTapEditor
