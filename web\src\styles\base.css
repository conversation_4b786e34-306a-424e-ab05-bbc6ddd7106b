.display-block {
  display: block;
}

.cursor-pointer {
  cursor: pointer;
}

.display-flex {
  display: flex;
}

.display-none {
  display: none;
}

.flex-column-start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.flex-column-end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flex-column-gap12 {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.flex-column-gap24 {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.padding-0 {
  padding: 0;
}

.padding-2 {
  padding: 2px;
}

.padding-4 {
  padding: 4px;
}

.margin-0 {
  margin: 0;
}

.mr-1 {
  margin-right: 1rem;
}

.mr-2 {
  margin-right: 2rem;
}

.background-white {
  background-color: white;
}

:root {
  --white: #fff;
  --white-1: #f0f0f0;
  --white-2: #ddd;
  --creamy-white-1: #eeece4;
  --creamy-white-2: #e4e1d8;
  --creamy-white-3: #dcd8ca;
  --black: #2e2b29;
  --black-contrast: #110f0e;
  --gray-1: rgba(61, 37, 20, 5%);
  --gray-2: rgba(61, 37, 20, 8%);
  --gray-3: rgba(61, 37, 20, 12%);
  --gray-4: rgba(53, 38, 28, 30%);
  --gray-5: rgba(28, 25, 23, 60%);
  --green: #22c55e;
  --blue: #4ba3fd;
  --blue-light: #e6f3ff;
  --purple: #6a00f5;
  --purple-contrast: #5800cc;
  --purple-light: rgba(88, 5, 255, 5%);
  --yellow-contrast: #facc15;
  --yellow: rgba(250, 204, 21, 40%);
  --yellow-light: #fffae5;
  --red: #ff5c33;
  --red-light: #ffebe5;
  --border-1: 0.1rem solid var(--gray-3);
  --shadow: 0 0.25rem 0.6rem rgba(0, 0, 0, 10%);
}

/* 全局溢出控制 */
html,
body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  overscroll-behavior: none; /* 防止页面反弹 */
}

/* 阻止iOS设备上的弹性滚动 */
html {
  position: fixed;
  height: 100%;
  width: 100%;
  touch-action: manipulation;
}

/* 防止输入框自动缩放 */
input,
textarea,
select {
  font-size: 16px; /* 16px是iOS不会自动缩放的最小字体大小 */
  max-height: 100%; /* 确保不会导致页面错位 */
}

/* 确保所有容器在过渡期间不会出现滚动条 */
.article-container,
.comment-container {
  overscroll-behavior: none;
}
