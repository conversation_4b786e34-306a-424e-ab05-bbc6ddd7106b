import { useDialog } from 'naive-ui'
import { ref, computed, nextTick } from 'vue'

import articleApi from '@/api/article'
import fileApi from '@/api/file'
import {
  ArticlePublishedScope,
  ARTICLE_PUBLISHED_SCOPE_LABEL,
} from '@/constants/article_published_scope.constants'
import type { Article } from '@/types/article.types'
import type { ResponseData } from '@/types/response_data.types'
import message from '@/utils/message'
import { activeTheme, ThemeType } from '@/utils/theme'
import tiptap from '@/utils/tiptap'

import type { Span } from 'naive-ui/es/legacy-grid/src/interface'

export function useArticleList(props: any) {
  // 文章列表数据
  const articleList = ref<Article[]>([])
  const loading = ref(false)
  const noMore = ref(false)
  const cardColSpan = ref<Span>(6)
  const articleTiptapEditorMap = ref(new Map())

  // DOM引用
  const containerRef = ref<HTMLElement | null>(null)
  const scrollContainerRef = ref<HTMLElement | null>(null)

  // 对话框
  const dialog = useDialog()

  // 颜色配置
  const lightColors = [
    '#ffd6d6',
    '#ffe8d1',
    '#fff8c4',
    '#d5edd7',
    '#d0e8fa',
    '#ded6f2',
    '#ebcfe9',
    '#f8d4de',
  ]

  const darkColors = [
    '#8c3a3a',
    '#7d6339',
    '#75763a',
    '#366d5a',
    '#355678',
    '#534878',
    '#664766',
    '#6a4251',
  ]

  // 判断当前是否为暗色主题
  const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

  // 按彩虹顺序获取卡片颜色
  const getCardColor = (id: string, index: number) => {
    const colorSet = isDarkTheme.value ? darkColors : lightColors
    return colorSet[index % colorSet.length]
  }

  // 根据窗口宽度动态调整卡片列数
  const updateColSpan = () => {
    const width = window.innerWidth
    if (width >= 1680) {
      cardColSpan.value = 6
    } else if (width >= 1260) {
      cardColSpan.value = 8
    } else if (width >= 840) {
      cardColSpan.value = 12
    } else {
      cardColSpan.value = 24
    }
  }

  // 重置文章列表
  const resetList = () => {
    articleList.value = []
    noMore.value = false
    loadArticles()
  }

  // 加载文章
  const loadArticles = (loadMore = false, signal?: AbortSignal) => {
    const loadSize = 8

    if (loading.value || noMore.value) return Promise.resolve()

    loading.value = true

    const lastArticle =
      articleList.value.length > 0 ? articleList.value[articleList.value.length - 1] : null
    const requestId = lastArticle?.id

    return new Promise<void>((resolve, reject) => {
      nextTick(() => {
        const searchParams = { ...props.searchCondition, id: requestId, loadSize: loadSize }
        articleApi
          .search(searchParams, signal)
          .then((res: ResponseData) => {
            if (!res || !res.data) {
              resolve()
              return
            }

            const data = res.data
            if (data.length === 0) {
              noMore.value = true
              resolve()
              return
            }

            if (data.length < loadSize) {
              noMore.value = true
            }

            const newArticlesAdded = addArticles(data)

            if (!newArticlesAdded) {
              noMore.value = true
              resolve()
              return
            }

            // 自动加载更多逻辑
            if (!loadMore && !noMore.value && newArticlesAdded) {
              nextTick(() => {
                const container = containerRef.value
                const content = scrollContainerRef.value

                if (container && content) {
                  if (content.scrollHeight <= container.clientHeight) {
                    loadArticles(true, signal).catch(() => {})
                  }
                }
                resolve()
              })
            } else {
              resolve()
            }
          })
          .catch((error) => {
            if (error.name === 'CanceledError' || error.message === 'canceled') {
              resolve()
              return
            }

            console.error('加载文章失败:', error)
            noMore.value = false
            if (!signal?.aborted) {
              reject(error)
            } else {
              resolve()
            }
          })
          .finally(() => {
            if (!signal?.aborted) {
              loading.value = false
            }
          })
      })
    })
  }

  // 添加文章到列表
  const addArticles = (list: Article[]): boolean => {
    const existingArticleIds = new Set(articleList.value.map((article) => article.id))
    const newArticles = list.filter((article) => !existingArticleIds.has(article.id))

    if (newArticles.length === 0) return false

    const processedList = newArticles.map((article) => ({
      ...article,
      contentObj: tiptap.toJsonObject(article.content),
      publisherAvatar: getResourceURL(article.publisherAvatar),
      tags: article.tag?.split(',') || [],
    }))

    articleList.value = [...articleList.value, ...processedList]
    return true
  }

  // 获取资源URL
  const getResourceURL = (uri: string): string => {
    return fileApi.getResourceURL(uri)
  }

  // 切换文章发布范围
  const handleToggleScope = (article: Article) => {
    const currentScope = article.publishedScope
    const targetScope =
      currentScope === ArticlePublishedScope.PERSONAL
        ? ArticlePublishedScope.PUBLIC
        : ArticlePublishedScope.PERSONAL

    dialog.warning({
      title: '切换发布范围',
      content: `确定要将文章《${article.title}》切换为${ARTICLE_PUBLISHED_SCOPE_LABEL[targetScope]}可见吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        articleApi.togglePublishedScope(article.id).then((res: ResponseData) => {
          if (res.code === 200) {
            article.publishedScope = targetScope
            message.success(`文章已切换为${ARTICLE_PUBLISHED_SCOPE_LABEL[targetScope]}可见`)
          } else {
            message.error(res.message || '操作失败')
          }
        })
      },
    })
  }

  // 处理删除文章
  const handleDeleteArticle = (article: Article) => {
    dialog.warning({
      title: '删除文章',
      content: `确定要删除文章《${article.title}》吗？此操作不可恢复。`,
      positiveText: '确定删除',
      negativeText: '取消',
      onPositiveClick: () => {
        articleApi
          .delete(article.id)
          .then((res: ResponseData) => {
            if (res.code === 200) {
              const index = articleList.value.findIndex((item) => item.id === article.id)
              if (index > -1) {
                articleList.value.splice(index, 1)
              }
              message.success('文章已删除')
            } else {
              message.error(res.message || '删除失败')
            }
          })
          .catch(() => {
            message.error('删除失败，请稍后重试')
          })
      },
    })
  }

  // 处理文章重新排序
  const handleReorderArticles = (
    draggedId: string,
    targetId: string,
    position: 'before' | 'after',
  ) => {
    const draggedIndex = articleList.value.findIndex((item) => item.id === draggedId)
    const targetIndex = articleList.value.findIndex((item) => item.id === targetId)

    if (draggedIndex === -1 || targetIndex === -1) return

    const [draggedArticle] = articleList.value.splice(draggedIndex, 1)

    let newIndex = targetIndex
    if (position === 'after') {
      newIndex = draggedIndex < targetIndex ? targetIndex : targetIndex + 1
    } else {
      newIndex = draggedIndex > targetIndex ? targetIndex : targetIndex
    }

    articleList.value.splice(newIndex, 0, draggedArticle)
    message.success('移动成功！')
  }

  return {
    // 数据
    articleList,
    loading,
    noMore,
    cardColSpan,
    articleTiptapEditorMap,
    containerRef,
    scrollContainerRef,

    // 计算属性
    getCardColor,

    // 方法
    updateColSpan,
    resetList,
    loadArticles,
    handleToggleScope,
    handleDeleteArticle,
    handleReorderArticles,
  }
}
