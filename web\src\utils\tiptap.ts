import Blockquote from '@tiptap/extension-blockquote'
import Bold from '@tiptap/extension-bold'
import BubbleMenu from '@tiptap/extension-bubble-menu'
import BulletList from '@tiptap/extension-bullet-list'
import Code from '@tiptap/extension-code'
import Color from '@tiptap/extension-color'
import Document from '@tiptap/extension-document'
import Dropcursor from '@tiptap/extension-dropcursor'
import FloatingMenu from '@tiptap/extension-floating-menu'
import Focus from '@tiptap/extension-focus'
import Gapcursor from '@tiptap/extension-gapcursor'
import Heading from '@tiptap/extension-heading'
import Highlight from '@tiptap/extension-highlight'
import History from '@tiptap/extension-history'
import HorizontalRule from '@tiptap/extension-horizontal-rule'
import Italic from '@tiptap/extension-italic'
import Link from '@tiptap/extension-link'
import ListItem from '@tiptap/extension-list-item'
import OrderedList from '@tiptap/extension-ordered-list'
import Paragraph from '@tiptap/extension-paragraph'
import Strike from '@tiptap/extension-strike'
import TaskList from '@tiptap/extension-task-list'
import Text from '@tiptap/extension-text'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import Typography from '@tiptap/extension-typography'
import Underline from '@tiptap/extension-underline'
import { all, createLowlight } from 'lowlight'
import { Markdown } from 'tiptap-markdown'

import Bilibili from '@/components/tiptap/extensions/bilibili'
import CustomCodeBlock from '@/components/tiptap/extensions/code-block'
import Fullscreen from '@/components/tiptap/extensions/fullscreen'
import Image from '@/components/tiptap/extensions/image'
import Mention from '@/components/tiptap/extensions/mention'
import CustomTaskItem from '@/components/tiptap/extensions/task-item'
import config from '@/config'
import { HTTPS } from '@/constants/proto.constants'
import Json from '@/utils/json'
import logger from '@/utils/log'

import type { Extension, AnyExtension, JSONContent } from '@tiptap/vue-3'

const lowlight = createLowlight(all)

const extensionEntries: Array<[string, Extension | AnyExtension]> = [
  ['document', Document],
  ['paragraph', Paragraph],
  ['text', Text],
  ['image', Image],
  ['dropcursor', Dropcursor],
  ['bold', Bold],
  ['italic', Italic],
  ['strike', Strike],
  ['underline', Underline],
  ['code', Code],
  ['heading', Heading],
  [
    'bulletList',
    BulletList.configure({
      keepMarks: true,
    }),
  ],
  [
    'orderedList',
    OrderedList.configure({
      keepMarks: true,
    }),
  ],
  ['listItem', ListItem],
  [
    'taskList',
    TaskList.configure({
      itemTypeName: 'taskItem',
    }),
  ],
  [
    'taskItem',
    CustomTaskItem.configure({
      nested: true,
      onReadOnlyChecked: (_node, _checked) => {
        // 在只读模式下默认阻止复选框状态改变
        return false
      },
    }),
  ],
  ['blockquote', Blockquote],
  ['textStyle', TextStyle],
  [
    'color',
    Color.configure({
      types: ['textStyle'],
    }),
  ],
  [
    'backgroundColor',
    Highlight.configure({
      multicolor: true,
    }),
  ],
  [
    'codeBlockLowlight',
    CustomCodeBlock.configure({
      lowlight,
    }),
  ],
  ['horizontalRule', HorizontalRule],
  [
    'link',
    Link.configure({
      defaultProtocol: 'https',
    }),
  ],
  ['history', History],
  ['typography', Typography],
  [
    'markdown',
    Markdown.configure({
      transformPastedText: true,
    }),
  ],
  [
    'focus',
    Focus.configure({
      mode: 'deepest',
    }),
  ],
  ['gapcursor', Gapcursor],
  ['mention', Mention as Extension],
  ['bilibili', Bilibili as Extension],
  ['floatingMenu', FloatingMenu],
  ['bubbleMenu', BubbleMenu],
  [
    'align',
    TextAlign.configure({
      types: ['heading', 'paragraph', 'blockquote'],
      alignments: ['left', 'center', 'right', 'justify'],
      defaultAlignment: 'left',
    }),
  ],
  ['fullscreen', Fullscreen],
]

const tiptap = {
  extensionMap: new Map(extensionEntries),
  replaceImageUrls: (content: JSONContent, domain: boolean = false) => {
    // 初始化图片扩展以获取路径处理方法
    const imageExtension = Image
    const transformSrc = imageExtension.storage.transformSrc
    const getFullUrl = imageExtension.storage.getFullUrl

    content?.content?.forEach((node: JSONContent) => {
      // 检查是否是图片节点
      if (node.type === 'image' && node.attrs && node.attrs.src) {
        // 根据domain参数决定是替换为完整URL还是相对路径
        if (domain) {
          // 使用完整URL（只在显示时需要）
          if (getFullUrl) {
            node.attrs.src = getFullUrl(node.attrs.src)
          } else {
            node.attrs.src = node.attrs.src.replace(HTTPS, config.backend.resourceURL)
          }
        } else {
          // 转换为相对路径（保存或其他场景使用）
          if (transformSrc) {
            node.attrs.src = transformSrc(node.attrs.src)
          } else {
            // 如果图片扩展的方法不可用，使用简单替换
            const baseUrl = config.backend.resourceURL
            if (node.attrs.src.startsWith(baseUrl)) {
              node.attrs.src = node.attrs.src.substring(baseUrl.length)
            }
          }
        }
      }
      // 递归遍历子节点
      if (node.content) {
        tiptap.replaceImageUrls(node, domain)
      }
    })
  },
  toJsonString: (content: JSONContent): string => {
    // 初始化图片扩展以获取路径处理方法
    const imageExtension = Image
    const transformSrc = imageExtension.storage.transformSrc

    // 在转为JSON字符串前处理内容中的图片URL
    const contentCopy = JSON.parse(JSON.stringify(content)) // 深拷贝避免修改原对象

    // 递归处理图片节点
    const processImageUrls = (node: JSONContent) => {
      if (!node) return

      // 处理图片节点
      if (node.type === 'image' && node.attrs && node.attrs.src) {
        // 转换为相对路径
        const originalSrc = node.attrs.src
        // 使用图片扩展的方法转换路径
        if (transformSrc) {
          node.attrs.src = transformSrc(originalSrc)
        } else {
          // 如果图片扩展的方法不可用，使用简单替换
          const baseUrl = config.backend.resourceURL
          if (originalSrc.startsWith(baseUrl)) {
            node.attrs.src = originalSrc.substring(baseUrl.length)
          } else if (originalSrc.startsWith('http')) {
            try {
              const url = new URL(originalSrc)
              node.attrs.src = url.pathname
            } catch {
              logger.warn('Failed to parse URL:', originalSrc)
            }
          }
        }

        logger.debug('Image URL transformed:', {
          original: originalSrc,
          transformed: node.attrs.src,
        })
      }

      // 递归处理子节点
      if (node.content && Array.isArray(node.content)) {
        node.content.forEach(processImageUrls)
      }
    }

    // 处理所有节点
    processImageUrls(contentCopy)

    return Json.stringify(contentCopy)
  },
  /**
   * 将JSON字符串转换为JSONContent对象
   * 增强容错处理，防止无效JSON导致解析失败
   */
  toJsonObject: (content: string): JSONContent => {
    if (!content) {
      logger.warn('Empty content passed to toJsonObject')
      return { type: 'doc', content: [{ type: 'paragraph', content: [] }] }
    }

    try {
      // 首先检查是否已经是JSONContent对象
      if (typeof content === 'object') {
        return content as JSONContent
      }

      // 尝试解析JSON字符串
      const parsed = Json.parse(content) as JSONContent

      // 如果解析失败，返回一个基本的空文档结构
      if (!parsed) {
        logger.warn('Failed to parse content in toJsonObject, creating fallback structure')
        return {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: typeof content === 'string' ? content : '无法显示内容',
                },
              ],
            },
          ],
        }
      }

      return parsed
    } catch (error) {
      logger.error('Error in toJsonObject:', error)
      // 返回一个包含原文本的基本结构
      return {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: typeof content === 'string' ? content : '解析错误',
              },
            ],
          },
        ],
      }
    }
  },
  serializeContent: (content: JSONContent | null) => {
    let result = ''
    if (!content) return result
    // Helper function to process nodes recursively
    function processNode(node: JSONContent) {
      if (node.type === 'text') {
        // Append text content
        result += node.text
      } else if (node.type === 'mention') {
        // Append mention label
        result += '@' + node.attrs?.label
      } else if (node.type === 'image') {
        // Append [图片]
        result += '[图片]'
      } else if (node.content && Array.isArray(node.content)) {
        // Recursively process child nodes
        node.content.forEach((child: JSONContent) => processNode(child))
      }
    }
    // Start processing from the root content
    processNode(content)
    return result
  },
}
export default tiptap
