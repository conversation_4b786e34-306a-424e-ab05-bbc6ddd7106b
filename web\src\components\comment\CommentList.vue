<template>
  <div class="comment-list-container" :class="{ 'has-input-box': commentInputVisible == '-1' }">
    <NInfiniteScroll @load="handleLoadMore" :distance="50" :trigger="commentScrollTrigger">
      <div class="comment-scroll">
        <div
          v-for="commentItem in commentList"
          :key="commentItem.id"
          :ref="
            (el) => {
              if (el) updateCommentRef(commentItem.id, el as HTMLElement)
            }
          "
        >
          <CommentListItem
            :comment="commentItem"
            :flash-comment-id="flashCommentId"
            :show-reply-list-btn="showReplyListBtn"
            :comment-input-visible="commentInputVisible"
            :quick-reply-loading="quickReplyLoading"
            @show-reply-list="$emit('showReplyList', $event)"
            @handle-comment-reply-click="$emit('handleCommentReplyClick', $event)"
            @interaction-btn="handleInteractionBtn"
            @favorite-btn="$emit('favoriteBtn', $event)"
            @quick-reply-comment="$emit('quickReplyComment', $event)"
            @update-editor="handleUpdateEditor"
          />
        </div>
        <div class="comment-list-footer">
          <NSpin v-if="commentLoading" class="display-flex" />
          <NEmpty
            v-else-if="!commentLoading && (commentNoMore || commentList.length === 0)"
            :description="hasCommentPermission ? '没有更多评论了...' : '您没有权限查看评论'"
          />
        </div>
      </div>
    </NInfiniteScroll>
  </div>
</template>

<script lang="ts" setup>
import { NInfiniteScroll, NSpin, NEmpty } from 'naive-ui'

import CommentListItem from '@/components/comment/CommentListItem.vue'
import type { Comment } from '@/types/comment.types'
import type { EditorWithFormatPainter } from '@/types/tiptap.types'

const props = defineProps<{
  commentList: Comment[]
  flashCommentId: string
  showReplyListBtn: boolean
  commentInputVisible: string
  commentScrollTrigger: string
  commentLoading: boolean
  commentNoMore: boolean
  hasCommentPermission: boolean
  quickReplyLoading: Map<string, boolean>
}>()

const emit = defineEmits<{
  (e: 'loadMoreComments'): void
  (e: 'showReplyList', comment: Comment): void
  (e: 'handleCommentReplyClick', comment: Comment): void
  (e: 'interactionBtn', comment: Comment, actionType: number): void
  (e: 'favoriteBtn', comment: Comment): void
  (e: 'quickReplyComment', comment: Comment): void
  (e: 'updateEditor', commentId: string, editor: EditorWithFormatPainter): void
  (e: 'updateCommentRef', commentId: string, el: HTMLElement): void
}>()

const updateCommentRef = (commentId: string, el: HTMLElement) => {
  emit('updateCommentRef', commentId, el)
}

// 处理互动按钮点击
const handleInteractionBtn = (comment: Comment, actionType: number) => {
  emit('interactionBtn', comment, actionType)
}

// 处理编辑器更新
const handleUpdateEditor = (commentId: string, editor: EditorWithFormatPainter) => {
  emit('updateEditor', commentId, editor)
}

// 处理加载更多评论
const handleLoadMore = () => {
  // 当评论列表为空且已标记无更多评论时，不触发加载
  if (props.commentList.length === 0 && props.commentNoMore) {
    return
  }
  emit('loadMoreComments')
}
</script>

<style scoped lang="scss">
.comment-list-container {
  flex: 1;
  overflow-y: auto;
  position: relative;
  padding: 1.25rem 0 1.25rem 1.25rem;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex布局正常工作 */
  height: 100%; /* 确保容器占满可用高度 */

  &.has-input-box {
    padding-bottom: calc(1.25rem + 120px); /* 为输入框提供足够空间 */
  }

  .comment-scroll {
    flex: 1;
    padding-right: 1.25rem;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 确保flex布局正常工作 */
    height: 100%; /* 确保滚动区域占满容器高度 */

    .comment-list-footer {
      padding: 1.25rem 0;
      display: flex;
      justify-content: center;

      // margin-bottom: 20px;
      flex-shrink: 0; /* 防止footer被压缩 */
    }
  }
}

// 添加响应式设计，调整窄屏下的布局
@media (width <= 768px) {
  .comment-list-container {
    height: calc(100vh - 60px); /* 减去输入框的高度 */
  }
}
</style>
